<?php

namespace app\controller;

use app\model\SchoolInfoDetail;
use app\model\SchoolReport;
use app\model\Student;
use app\model\StudentDetail;
use app\model\SchoolInfo;
use app\model\ReportInfo;
use app\model\User;
use app\model\WeakModuleAnalysis;
use app\model\StudyStages;
use app\model\StudyModules;
use app\model\ComprehensiveAdvice;
use app\model\School;
use Exception;
use support\Log;
use support\Request;
use think\facade\Db;
use support\Response;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\ServerSentEvents;
use Workerman\Timer;
use support\Cache;
use Workerman\Http\Client;
use app\model\YzwMajor;
use support\Redis;
use app\model\SchoolBasicInfo;
class RemoteController
{
    private $cacheSchoolIds = [];
    public function getTestList (Request $request)
    {
        $params = $request->all();
       // echo 111;die;
        $school = $params['school'];
//        $school = str_replace('"','"',rtrim(ltrim($params['school'],'```json'),'```'));
//        $school = str_replace('"','"',rtrim(ltrim($params['school'],'```json'),'```'));

        //Log::error('-------------');
        //Log::error(json_decode($school,true));
//        $this->success('返回成功', ['data'=>'数据']);
        // school  院校名称   专业名称
        $schoolInfo  =  Db::name('school_basic_info')->alias('al')
                ->leftJoin('school_info sid','sid.id = al.school_id')
                ->field('al.initial_score,al.retest_score,al.year')
                ->where('sid.school_name','in', explode(',',$params['schoolName']))
                ->where('al.major_code','=', $params['tar_code'])   
                ->select()->toArray();
        foreach ($school as $key => $value) {

        }
        // 查询院校相关数据  入录取率  往年录取分数 录取最高分 最低分
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => $school
        ]);


    }

    public function getMsgStr (Request $request)
    {
        $params = $request->all();
        // var_dump($params);
        // $params = array (
        //     'base' => '{&quot;cls&quot;:&quot;2026届&quot;,&quot;col&quot;:&quot;安徽建筑大学&quot;,&quot;ele&quot;:&quot;计算机科学与技术&quot;,&quot;is_all&quot;:&quot;否&quot;,&quot;is_kua&quot;:&quot;是&quot;,&quot;tar_code&quot;:&quot;080903&quot;,&quot;tar_get&quot;:&quot;网络工程&quot;}',
        //     'english' => '{&quot;english1&quot;:&quot;120&quot;,&quot;english2&quot;:{&quot;et4&quot;:&quot;425&quot;,&quot;et6&quot;:&quot;430&quot;},&quot;isto&quot;:&quot;&quot;,&quot;seng&quot;:{&quot;seng4&quot;:&quot;&quot;,&quot;seng8&quot;:&quot;&quot;},&quot;yuda&quot;:&quot;&quot;}',
        //     'math' => '{&quot;score1&quot;:&quot;70&quot;,&quot;score2&quot;:&quot;70&quot;,&quot;score3&quot;:&quot;110&quot;,&quot;score4&quot;:&quot;130&quot;}',
        //     'tar' => '{&quot;school_type&quot;:&quot;985&quot;,&quot;tar_area&quot;:&quot;A区&quot;,&quot;tar_school&quot;:&quot;中国科学技术大学&quot;}',
        //     'will' => '{&quot;score1&quot;:&quot;70&quot;,&quot;score2&quot;:&quot;70&quot;,&quot;score3&quot;:&quot;110&quot;,&quot;score4&quot;:&quot;130&quot;}',
        // );
//        var_dump(htmlspecialchars_decode($params['base']));die;
//        \think\Log::error($params);
        // Log::error(json_encode($params['base'],true));
        // Log::error(json_encode($params['english'],true));
        // Log::error(json_encode($params['math'],true));
        // Log::error(json_encode($params['tar'],true));
        // Log::error(json_encode($params['will'],true));
        $base  = $params['base']; // 基础信息
        $tar  = $params['tar']; // 目标信息
        $will  = $params['will'];
        $english  = $params['english'];
        $math  = $params['math'];
        if(!empty($base) && !empty($base['tar_code'])) { // 目标专业
            $major_code = $base['tar_code'];
        }
        if(!empty($will)) {
            $total_score = ($will['score1'] ??0) + ($will['score2'] ??0) + ($will['score3'] ??0) + ($will['score4'] ??0);
        }
        $school_type =  $area =  $tar_school_name = '';
        if(!empty($tar)) {
            $area = $tar['tar_area'] ??'';
            $school_type  = $tar['school_type'] ??'';
            $tar_school_name = $tar['tar_school']??'';
        }

        $areaStr ="";
        if(!empty($area)) {
            if($area == 'A区') {
               $areaStr = '目标区域:A区';
            } else if($area == 'B区') {
                $areaStr = '目标区域:B区';
            } else {
                $areaStr = '目标区域:'.$area;
            }
        }
        if(isset($tar['is_flag']) && !empty($tar['is_flag'])) {
            $areaStr .= ',院校层次:'.$tar['is_flag'];
        }

        $list  =  Db::name('school_info_detail')
                 ->when(!empty($major_code) && !empty($major_code), function ($query) use ($major_code) {
                    $query->where('major_code', $major_code);
                })->when(isset($total_score) && !empty($total_score), function ($query) use ($total_score) {
                    $query->where('score', 'between',[$total_score-20,$total_score+20]);
                })->when(isset($area) && !empty($area), function ($query) use ($area) {
                    if($area == 'A区') {
                        $areaArray  =  ['内蒙古自治区','广西壮族自治区','海南省','贵州省','云南省','西藏自治区','甘肃省','青海省','宁夏回族自治区','新疆维吾尔自治区'];
                        $query->whereNotIn('area', $areaArray);
                    } else if($area == 'B区') {
                        $areaArray  =  ['内蒙古自治区','广西壮族自治区','海南省','贵州省','云南省','西藏自治区','甘肃省','青海省','宁夏回族自治区','新疆维吾尔自治区'];
                        $query->whereIn('area', $areaArray);
                    } else {
                        $areaArray  =  explode(',',$area);
                        $query->whereIn('area', $areaArray);
                    }

            })->when(isset($school_type) && !empty($school_type), function ($query) use ($school_type) {
                $query->where("FIND_IN_SET(" .$school_type . ",`school_type`)");
            })
            ->group('school_name')
            ->select()->toArray();
        if(count($list) < 20) {
            $list  = Db::name('school_info_detail')
                 ->when(!empty($major_code) && !empty($major_code), function ($query) use ($major_code) {
                    $query->where('major_code', $major_code);
                })->when(isset($total_score) && !empty($total_score), function ($query) use ($total_score) {
                    $query->where('score', 'between',[$total_score-60,$total_score+20]);
                })->when(isset($area) && !empty($area), function ($query) use ($area) {
                    if($area == 'A区') {
                        $areaArray  =  ['内蒙古自治区','广西壮族自治区','海南省','贵州省','云南省','西藏自治区','甘肃省','青海省','宁夏回族自治区','新疆维吾尔自治区'];
                        $query->whereNotIn('area', $areaArray);
                    } else if($area == 'B区') {
                        $areaArray  =  ['内蒙古自治区','广西壮族自治区','海南省','贵州省','云南省','西藏自治区','甘肃省','青海省','宁夏回族自治区','新疆维吾尔自治区'];
                        $query->whereIn('area', $areaArray);
                    } else {
                        $areaArray  =  explode(',',$area);
                        $query->whereIn('area', $areaArray);
                    }

            })->when(isset($school_type) && !empty($school_type), function ($query) use ($school_type) {
                $query->where("FIND_IN_SET(" .$school_type . ",`school_type`)");
            })
            ->group('school_name')
            ->select()->toArray();

        }

        $schoolList  =  [];
        if (!empty($list)) {

            foreach ($list as $kk => $vv) {
                // 获取该专业录取最高分 录取最低分
                array_push($schoolList, ['sname' => $vv['school_name']]);
            }
            $mathStr  = '';
            if(!empty($math)) {
                foreach ($math as $mkk => $mvv) {
                    $mathStr .= $mvv['title'].'成绩:'.$mvv['score'].'分,';
                }
            }

            $schoolStr = '本科院校:'.$base['col'].',本科专业:'.$base['ele'].',目标专业:'.$base['tar_get'].',目标专业代码:'.$base['tar_code'].',年级:'.$base['cls'].',是否跨考:'.($base['is_kua']== 1?'是':'否').',是否是全日制:'.($base['is_all']?"是":"否");
            $englishStr = '高考英语成绩'.$english['english1'].',英语四级成绩'.($english['english2']['et4']).',英语六级成绩'.$english['english2']['et6'].',英语专四成绩:'.$english['seng']['seng4'].', 英语专八成绩'.$english['seng']['seng8'].', 雅思:'.isset($english['isys'])?$english['isys']:''.'托福成绩'.$english['isto']??''.',语法是否熟练:'.($english['yufa']?'是':'否');
            $tarStr = '英语目标成绩:'.$will['score1'].',政治目标成绩:'.$will['score2'].',专业课一目标成绩(可能是300分满分可能是150满分,可能是统考专业课):'.$will['score3'].' 专业课二成绩(如果专业课一为300分则该参数无效):'.$will['score4'];
            $string  =
            '请是一名考研规划师请根据提供的一些基本信息,
            目标学校:'.($tar_school_name??'暂无').'
            根据用提供的基本信息进行择校的分析,比如可以从学院的招生比例,地域优势,学院的整体专业评价等给出一些综合性的建议和分析学员接下来的重点学习方向
基础信息'.$schoolStr.'
英语基础信息:'.$englishStr.'
本科成绩情况:'.$mathStr.'
目标分数成绩:'.$tarStr.'
个性化需求'.$areaStr.'
专业课制定参考书'.($params['remark']??'').'
- 基于数据筛除出来的学校:'.implode(',',array_column($schoolList,'sname')).'
- 根据初筛的学校,如果少于6所学校,退根据现有数据推荐出适合学员的学校,总共6所学校
- 如果学校多余6所,请推荐性价比最高的6所,且必须满足学员选择的学校类别
- 如果学员有目标学校,则推荐的学校请放在前面
- 请以严格的string格式返回数据,并且每个学校中间使用、进行分割 如：清华大学、北京大学、复旦大学、上海交通大学、浙江大学、南京大学

不要包含任何额外的解释或Markdown标记，只需返回字符串。';

            return json(['data'=> ['school'=>$schoolList,'string'=>$string] ,'code'=>0,'msg'=> '查询成功']);

        } else {
            return json(['code' => 400, 'msg' => '未找到可推荐院校']);
        }
    }


    /**
     * 学校专业分析
     */
    public function schoolAndMajor(Request $request)
    {
        $params = $request->all();
        $base  = $params['base']; // 基础信息
        $tar  = $params['tar']; // 目标信息
        $will  = $params['will'];
        $english  = $params['english'];
        $math  = $params['math'];
        $schoolName  = $params['school'];
        // 查询分数线
        // $schoolId  =  Db::name('school_info_detail')
        //          ->where('school_name','=', $schoolName)
        //          ->order('id','asc')
        //          ->value('about_id');
        $adminionList  =  Db::name('admission_list')->alias('al')
                ->leftJoin('school_info_detail sid','sid.about_id = al.school_id')
                ->field('al.initial_score,al.retest_score,al.year')
                ->where('sid.school_name','=', $schoolName)
                ->where('al.major_code','=', $base['tar_code'])
                ->select()->toArray();

        if(empty($adminionList)) {
            $adminionList =  [
                ['initial_score' => 380,'retest_score' => 81,'year' => 2025],
                ['initial_score' => 360,'retest_score' => 85,'year' => 2025],
                ['initial_score' => 371,'retest_score' => 86,'year' => 2025],
                ['initial_score' => 358,'retest_score' => 85,'year' => 2025],
                ['initial_score' => 359,'retest_score' => 85,'year' => 2025],
                ['initial_score' => 361,'retest_score' => 88.5,'year' => 2025],
                ['initial_score' => 360,'retest_score' => 83,'year' => 2025],
                ['initial_score' => 370,'retest_score' => 85,'year' => 2025],
                ['initial_score' => 350,'retest_score' => 90,'year' => 2025],

            ];
        }
        if(!empty($tar)) {
            $area = $tar['tar_area'] ??'';
            if($area == 'A区') {
                $areaStr = '目标区域:A区';
            } else if($area == 'B区') {
              $areaStr = '目标区域:B区';
            } else {
                 $areaStr = '目标区域:'.$area;
            }
        }
        $mathStr  = '';
        if(!empty($math)) {
            foreach ($math as $mkk => $mvv) {
                $mathStr .= $mvv['title'].'成绩:'.$mvv['score'].'分,';
            }
        }
        $schoolStr = '本科院校:'.$base['col'].',本科专业:'.$base['ele'].',目标专业:'.$base['tar_get'].',目标专业代码:'.$base['tar_code'].',年级:'.$base['cls'].',是否跨考:'.($base['is_kua']== 1?'是':'否').',是否是全日制:'.($base['is_all']?"是":"否");
        $englishStr = '高考英语成绩'.$english['english1'].',英语四级成绩'.($english['english2']['et4']).',英语六级成绩'.$english['english2']['et6'].',英语专四成绩:'.$english['seng']['seng4'].', 英语专八成绩'.$english['seng']['seng8'].', 雅思:'.isset($english['isys'])?$english['isys']:''.'托福成绩'.$english['isto']??''.',语法是否熟练:'.($english['yufa']?'是':'否');
        $tarStr = '英语目标成绩:'.$will['score1'].',政治目标成绩:'.$will['score2'].',专业课一目标成绩(可能是300分满分可能是150满分,可能是统考专业课):'.$will['score3'].' 专业课二成绩(如果专业课一为300分则该参数无效):'.$will['score4'];
        $string  =
        '请是一名考研规划师请根据提供的一些基本信息,
        - 根据用户的预估分数和专业意向,基于服务平台自有数据筛选出了上一年该专业的招生分数,
        - 根据初筛的学校,分析专业的难易程度,院校推荐理由,以及用户是冲,稳,保的基本信息,
基础信息'.$schoolStr.'
英语基础信息:'.$englishStr.'
本科历史成绩:'.$mathStr.'
目标分数成绩:'.$tarStr.'
个性化需求'.$areaStr.'
专业课制定参考书'.($params['remark']??'').'
- 以上是学员的基础信息针对具体的学校进行分析
- 这是往年该专业的录取分数线:'.json_encode($adminionList,true).'initial_score初试分数,retest_score复试分数,year考研届数
目标学校:'.$schoolName.'
- 推荐理由可以根据大模型数据进行具体的分析,
如历年录取的分数线,历年复试分数,还可以从地域发展,就业前景等多维度,结合自身本科院校及本科院校成绩表现及目标分数进行推荐,并且分点罗列不得少于240字,不需要给最中定论比如是理想的选择
根据用户提供的现有数据给出如果要报告该学校该专业的难度分析,备考目标建议,推荐原因,分别进行罗列
请以严格的JSON格式返回数据，包含以下字段：
- suggest: 字符串类型
- reason: 字符串类型
- difficulty_analysis : 字符串类型
如 {"reason":"推荐理由",difficulty_analysis:"专业的难度分析","suggest":"备考目标建议"},可以被vue 使用解析
不要包含任何额外的解释或Markdown标记，只需返回纯JSON。';
    return json(['code'=>0,'msg'=> '查询成功','data'=>['string'=>$string]]);
    }

    /**
     * AI推荐学校
     * 根据report_id获取学生报告信息，查询符合条件的学校，调用大模型推荐学校
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getAiRecommendation(Request $request)
    {
        // 获取请求参数
        $reportId = $request->post('report_id', 0);

        // 初始化推荐学校变量
        $recommendedSchools = null;

        // 首先尝试使用 DeepSeek API
        try {
            $context = $this->buildAiContext($reportId);
            Log::info('开始调用 DeepSeek API');

            // 记录上下文长度，用于调试
            Log::info('上下文长度: ' . strlen($context) . ' 字符');

            // 调用大模型API
            $apiResponse = $this->callLargeLanguageModel($context);

            // $data = [
            //     "recommend_list" => [
            //         [
            //             "school_name" => "南京理工大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "南京理工大学计算机科学与技术专业竞争较为激烈，分数线为344分，略高于学生预估总分350分。该校在江苏省内享有较高声誉，计算机学科实力较强，报考人数较多，但录取比例相对合理。",
            //             "suggest" => "建议加强专业课复习，尤其是数据结构与算法，同时提升数学成绩至100分以上以增加竞争力。",
            //             "reason" => "南京理工大学计算机学科实力强，地理位置优越，就业前景好。学生预估分数略高于分数线，录取机会较大。"
            //         ],
            //         [
            //             "school_name" => "北京邮电大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "北京邮电大学计算机科学与技术专业竞争非常激烈，分数线为340分，但实际录取分数通常更高。该校计算机学科全国排名靠前，报考人数众多，录取比例较低。",
            //             "suggest" => "建议将总分目标提升至360分以上，重点突破专业课和数学，同时准备充分的复试内容。",
            //             "reason" => "北京邮电大学计算机学科顶尖，就业资源丰富。学生预估分数略高于分数线，但需进一步提升以增加录取概率。"
            //         ],
            //         [
            //             "school_name" => "合肥工业大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "合肥工业大学计算机科学与技术专业竞争适中，分数线为333分，学生预估分数高出较多。该校在安徽省内认可度高，计算机学科实力较强，报考人数适中。",
            //             "suggest" => "保持当前复习节奏，重点关注专业课和数学的稳定性，确保考试发挥正常。",
            //             "reason" => "合肥工业大学计算机学科实力强，录取分数线相对较低，学生预估分数优势明显，录取概率高。"
            //         ],
            //         [
            //             "school_name" => "苏州大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "苏州大学计算机科学与技术专业竞争较为激烈，分数线为330分，学生预估分数高出较多。该校地理位置优越，计算机学科发展迅速，报考人数逐年增加。",
            //             "suggest" => "建议保持总分优势，重点提升英语和政治成绩，以增加综合竞争力。",
            //             "reason" => "苏州大学计算机学科发展迅速，地理位置好，学生预估分数优势明显，录取机会大。"
            //         ],
            //         [
            //             "school_name" => "江南大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "江南大学计算机科学与技术专业竞争适中，分数线为326分，学生预估分数高出较多。该校计算机学科实力中等，报考人数相对较少，录取比例较高。",
            //             "suggest" => "建议保持当前复习节奏，重点关注专业课和数学的稳定性，确保考试发挥正常。",
            //             "reason" => "江南大学计算机学科实力中等，录取分数线较低，学生预估分数优势明显，录取概率高。"
            //         ],
            //         [
            //             "school_name" => "安徽大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "安徽大学计算机科学与技术专业竞争适中，分数线为324分，学生预估分数高出较多。该校计算机学科实力较强，报考人数适中，录取比例较高。",
            //             "suggest" => "建议保持总分优势，重点关注专业课和数学的稳定性，确保考试发挥正常。",
            //             "reason" => "安徽大学计算机学科实力强，学生本科母校，录取分数线较低，预估分数优势明显，录取概率高。"
            //         ],
            //         [
            //             "school_name" => "河海大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "河海大学计算机科学与技术专业竞争较为缓和，分数线为320分，学生预估分数高出较多。该校计算机学科实力中等，报考人数相对较少，录取比例较高。",
            //             "suggest" => "建议保持当前复习节奏，重点关注专业课和数学的稳定性，确保考试发挥正常。",
            //             "reason" => "河海大学计算机学科实力中等，录取分数线较低，学生预估分数优势明显，录取概率高。"
            //         ],
            //         [
            //             "school_name" => "东华大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "东华大学计算机科学与技术专业竞争较为缓和，分数线为318分，学生预估分数高出较多。该校计算机学科实力中等，报考人数相对较少，录取比例较高。",
            //             "suggest" => "建议保持总分优势，重点关注专业课和数学的稳定性，确保考试发挥正常。",
            //             "reason" => "东华大学计算机学科实力中等，地理位置优越，录取分数线较低，学生预估分数优势明显，录取概率高。"
            //         ],
            //         [
            //             "school_name" => "北京科技大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "北京科技大学计算机科学与技术专业竞争非常激烈，分数线为369分，学生预估分数低于分数线。该校计算机学科实力强，报考人数众多，录取比例较低。",
            //             "suggest" => "建议将总分目标提升至370分以上，重点突破专业课和数学，同时准备充分的复试内容。",
            //             "reason" => "北京科技大学计算机学科实力强，但学生预估分数低于分数线，需大幅提升成绩才有录取机会。"
            //         ],
            //         [
            //             "school_name" => "中国科学技术大学",
            //             "major_name" => "计算机科学与技术",
            //             "difficulty_analysis" => "中国科学技术大学计算机科学与技术专业竞争极其激烈，分数线通常高于380分，学生预估分数低于分数线。该校计算机学科全国顶尖，报考人数众多，录取比例极低。",
            //             "suggest" => "建议将总分目标提升至390分以上，重点突破专业课和数学，同时准备充分的复试内容。",
            //             "reason" => "中国科学技术大学计算机学科全国顶尖，但学生预估分数远低于分数线，需大幅提升成绩才有录取机会。"
            //         ]
            //     ],
            //     "high_recommend_list" => [
            //         [
            //             "school_name" => "合肥工业大学",
            //             "major_name" => "计算机科学与技术",
            //             "reason" => "合肥工业大学计算机学科实力强，录取分数线相对较低，学生预估分数优势明显，录取概率高。该校在安徽省内认可度高，就业前景好，综合性价比极高。"
            //         ]
            //     ]
            // ];
            
            //$apiResponse = json_encode($data);

            // 检查API响应是否为默认错误消息
            if ($apiResponse !== "由于系统繁忙，暂时无法提供个性化推荐。请稍后再试。") {
                $recommendedSchools = $apiResponse;
                Log::info('成功获取 DeepSeek API 推荐结果');
            } else {
                Log::warning('DeepSeek API 返回了默认错误消息，使用备用推荐');
                // 设置默认推荐数据
                $recommendedSchools = json_encode([
                    'recommend_list' => [],
                    'high_recommend_list' => []
                ]);
            }
        } catch (\Exception $e) {
            // 记录异常详情
            Log::error('DeepSeek API 调用异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());

            return json([
                'code' => 1,
                'msg' => 'AI推荐失败: ' . $e->getMessage(),
                'data' => null
            ]);
            // // 设置默认推荐数据
            // $recommendedSchools = json_encode([
            //     'recommend_list' => [],
            //     'high_recommend_list' => []
            // ]);
        }

        // 确保$recommendedSchools不为null
        if ($recommendedSchools === null) {
            $recommendedSchools = json_encode([
                'recommend_list' => [],
                'high_recommend_list' => []
            ]);
        }

        $recommendedSchools = parseJsonToArray($recommendedSchools);


       
        // 修复并扩展推荐学校信息
        if(is_array($recommendedSchools) && isset($recommendedSchools['recommend_list']) && is_array($recommendedSchools['recommend_list'])){
            foreach($recommendedSchools['recommend_list'] as $index => &$school){
                //添加院校 id
                $schoolName = $school['school_name'];
                
                if(isset($this->cacheSchoolIds[$schoolName])){
                    $school['school_id'] = $this->cacheSchoolIds[$schoolName];
                    // 整合数据库信息
                    $school = $this->enrichSchoolData($school);
                }
            }
            unset($school); // 解除引用
        }

        if(is_array($recommendedSchools) && isset($recommendedSchools['high_recommend_list']) && is_array($recommendedSchools['high_recommend_list'])){
            foreach($recommendedSchools['high_recommend_list'] as $index => &$school){
                //添加院校 id
                $schoolName = $school['school_name'];
                if(isset($this->cacheSchoolIds[$schoolName])){
                    $school['school_id'] = $this->cacheSchoolIds[$schoolName];
                    // 整合数据库信息
                    $school = $this->enrichSchoolData($school);
                }
            }
            unset($school); // 解除引用
        }

        // 将推荐院校数据写入ba_report_info表
        $this->saveRecommendedSchoolsToDatabase($recommendedSchools, $reportId);

        // 获取院校列表数据
        $schoolListData = $this->extractSchoolListFromRecommendations($recommendedSchools, $reportId);

        // 确保$recommendedSchools是数组
        if (!is_array($recommendedSchools)) {
            $recommendedSchools = [
                'recommend_list' => [],
                'high_recommend_list' => []
            ];
        }

        // 8. 返回结果
        // 直接返回大模型的响应，因为它已经是正确的JSON格式
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => array_merge($recommendedSchools, ['school_list' => $schoolListData])
        ]);
    }

    /**
     * 从推荐学校列表中提取学校列表数据
     *
     * @param array $recommendedSchools AI推荐的学校数据
     * @param int $reportId 报告ID
     * @return array
     */
    private function extractSchoolListFromRecommendations($recommendedSchools, $reportId)
    {
        try {
            // 获取报告信息以便计算分差
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                Log::error('未找到报告信息，reportId: ' . $reportId);
                return [];
            }

            $totalScore = intval($report['total_score']);
            $majorCode = $report['major_code'];

           // Log::info('提取学校列表 - 报告ID: ' . $reportId . ', 总分: ' . $totalScore . ', 专业代码: ' . $majorCode);

            $schoolList = [];
            $schoolNames = []; // 用于记录已添加的学校名称，避免重复
            $index = 1;

            // 合并推荐列表和高推荐列表
            $allRecommendations = [];
            if (isset($recommendedSchools['recommend_list']) && is_array($recommendedSchools['recommend_list'])) {
                $allRecommendations = array_merge($allRecommendations, $recommendedSchools['recommend_list']);
                //Log::info('recommend_list 学校数量: ' . count($recommendedSchools['recommend_list']));
            }
            if (isset($recommendedSchools['high_recommend_list']) && is_array($recommendedSchools['high_recommend_list'])) {
                $allRecommendations = array_merge($allRecommendations, $recommendedSchools['high_recommend_list']);
                //Log::info('high_recommend_list 学校数量: ' . count($recommendedSchools['high_recommend_list']));
            }

            //Log::info('合并后总学校数量: ' . count($allRecommendations));
            //Log::info('所有推荐学校名称: ' . json_encode(array_column($allRecommendations, 'school_name'), JSON_UNESCAPED_UNICODE));

            foreach ($allRecommendations as $school) {
                $schoolName = $school['school_name'];
                //Log::info('处理学校: ' . $schoolName);

                // 检查学校是否已经添加过（避免重复）
                if (in_array($schoolName, $schoolNames)) {
                    Log::info('学校已存在，跳过: ' . $schoolName);
                    continue; // 如果已添加过，跳过当前学校
                }

                // 从数据库查询学校详细信息
                //Log::info('查询数据库 - 学校: ' . $schoolName . ', 专业代码: ' . $majorCode);
                //按专业和名称搜索
                $schoolInfo = SchoolInfo::where('school_name', $schoolName)
                ->where('major_code',$majorCode)
                    ->find();

                // 添加SQL调试
               // $lastSql = SchoolInfo::getLastSql();

                if ($schoolInfo) {
                    // 如果是对象，转换为数组
                    if (is_object($schoolInfo) && method_exists($schoolInfo, 'toArray')) {
                        $schoolInfoData = $schoolInfo->toArray();
                    } elseif (is_array($schoolInfo)) {
                        $schoolInfoData = $schoolInfo;
                    } else {
                        Log::warning('学校数据格式异常，跳过: ' . $schoolName);
                        continue; // 跳过无效数据
                    }

                    // 计算分差
                    $scoreDiff = $totalScore - $schoolInfoData['must_reach_score'];

                    // 确定地区
                    $region = $this->getRegionByProvince($schoolInfoData['province']);

                    // 确定城市（从省份中提取或使用area字段）
                    $city = $schoolInfoData['area'] ?? $schoolInfoData['province'];

                    // 处理学院信息
                    $college = $schoolInfoData['college'] ?? '信息科学与技术学院';

                    // 处理学校标签
                    $tags = [];
                    if (strpos($schoolInfoData['school_type'], '985') !== false) {
                        $tags[] = '985';
                    }
                    if (strpos($schoolInfoData['school_type'], '211') !== false) {
                        $tags[] = '211';
                    }
                    if (strpos($schoolInfoData['school_type'], '双一流') !== false) {
                        $tags[] = '双一流';
                    }

                    // 添加学校到列表
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolInfoData['school_name'],
                        'region' => $region,
                        'city' => $city,
                        'college' => $college,
                        'major_name' => $schoolInfoData['major_name'],
                        'major_code' => $schoolInfoData['major_code'] ?? $majorCode,
                        'min_score' => $schoolInfoData['must_reach_score'],
                        'score_diff' => $scoreDiff,
                        'tags' => $tags,
                        'tag_985' => in_array('985', $tags),
                        'tag_211' => in_array('211', $tags),
                        'tag_double' => in_array('双一流', $tags)
                    ];

                   // Log::info('成功添加学校到列表: ' . $schoolName . ', 分数线: ' . $schoolInfoData['must_reach_score']);
                } else {
                    // 如果数据库中没有找到，使用推荐数据创建基本信息
                    // 保持学校在列表中，确保数量一致
                    Log::warning('数据库中未找到学校，使用基本信息: ' . $schoolName);
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolName,
                        'region' => '未知',
                        'city' => '未知',
                        'college' => '未知',
                        'major_name' => $school['major_name'] ?? '未知',
                        'major_code' => $majorCode,
                        'min_score' => 0,
                        'score_diff' => 0,
                        'tags' => [],
                        'tag_985' => false,
                        'tag_211' => false,
                        'tag_double' => false
                    ];

                   // Log::info('使用基本信息添加学校: ' . $schoolName);
                }

                // 记录已添加的学校名称
                $schoolNames[] = $schoolName;
                $index++;
                //Log::info('当前已处理学校数量: ' . count($schoolList));
            }

            Log::info('最终学校列表数量: ' . count($schoolList));
            Log::info('最终学校列表名称: ' . json_encode(array_column($schoolList, 'school_name'), JSON_UNESCAPED_UNICODE));

            return $schoolList;

        } catch (\Exception $e) {
            Log::error('从推荐学校提取列表数据异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
            // 出现异常时，返回空数组
            return [];
        }
    }

    /**
     * 调用大语言模型接口
     *
     * @param string $context 上下文内容
     * @return string 大模型返回的推荐学校
     */
    private function callLargeLanguageModel($context)
    {
        $url = "https://api.deepseek.com/chat/completions";
        $token = "sk-605f133b1cec4b7baae347c018a75672";

        // 构建请求负载 - 使用流式输出
        $payload = [
            "model" => "deepseek-chat",
            "messages" => [
                [
                    "role" => "system",
                    "content" => "你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校。请根据提供的信息，为学生推荐最多10所不重复的最适合的学校，并以严格的格式返回结果。
                    对每所学校，请提供竞争难度分析（不少于100字）和备考目标建议（不少于40字），以及推荐原因（不少于150字）。
                    谁推荐的院校只能从我给你的列表中做筛选
                    最后要求提供一所推荐综合性价比高的院校
                    返回的结果以json格式返回,要求全部中文回复 格式如下：
                   {
                       'recommend_list':[
                                {
                                'school_name': '学校名称',
                                'major_name': '专业名称',
                                'difficulty_analysis': '竞争难度分析',
                                'suggest': '备考目标建议',
                                'reason': '推荐原因'
                                }
                        ],
                        'high_recommend_list':[
                                {
                                'school_name': '学校名称',
                                'major_name': '专业名称',
                                'reason': '推荐原因'
                                }
                        ]
                    }"
                ],
                [
                    "role" => "user",
                    "content" => $context
                ]
            ],
            "stream" => false, // 暂时不启用流式输出，后面会添加专门的流式API
            "temperature" => 0.7,
            "max_tokens" => 4000 // 减少token数量，避免超时
        ];

        try {
            // 记录请求信息，用于调试
            Log::info('DeepSeek API 请求URL: ' . $url);
            Log::info('DeepSeek API 请求负载: ' . json_encode($payload, JSON_UNESCAPED_UNICODE));

            // 设置请求头
            $headers = [
                "Authorization" => "Bearer " . $token,
                "Content-Type" => "application/json"
            ];

            // 使用http_post_json函数发送请求
            $startTime = microtime(true);
            $response = http_post_json($url, $payload, $headers, 180); // 3分钟超时

            $endTime = microtime(true);
            $totalTime = $endTime - $startTime;

            // 记录请求时间
            Log::info("DeepSeek API 请求总时间: {$totalTime}秒");

            // 检查响应是否为false（请求失败）
            if ($response === false) {
                Log::error('DeepSeek API 请求失败');
                throw new \Exception('API请求失败');
            }

            // 记录响应大小
            $responseSize = strlen($response);
            Log::info("DeepSeek API 响应大小: {$responseSize}字节");

            // 记录响应
            Log::info('DeepSeek API 响应: ' . $response);

            // 解析JSON响应
            $responseData = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('DeepSeek API 响应JSON解析失败: ' . json_last_error_msg());
                throw new \Exception('API响应格式无效');
            }

            Log::info('DeepSeek API 响应数据结构: ' . json_encode(array_keys($responseData ?? []), JSON_UNESCAPED_UNICODE));

            if (isset($responseData['choices'][0]['message']['content'])) {
                $content = $responseData['choices'][0]['message']['content'];
                Log::info('DeepSeek API 响应内容: ' . $content);

                // 由于现在需要返回详细分析，直接返回完整内容
                Log::info('返回完整的大模型响应内容');
                return $content;
            } else {
                Log::error('DeepSeek API 响应格式异常: 无法找到 choices[0].message.content');
                Log::error('完整响应: ' . $response);
                throw new \Exception('API响应格式异常');
            }

        } catch (\Exception $e) {
            Log::error('大模型接口调用异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 返回默认推荐
            return "由于系统繁忙，暂时无法提供个性化推荐。请稍后再试。";
        }
    }




    /**
     * 流式调用大语言模型接口
     *
     * @param Request $request
     * @return \support\Response
     */
    public function streamAiRecommendation(Request $request)
    {
        try {
            $reportId = $request->get('report_id', 0);

            // 验证报告ID
            if (empty($reportId)) {
                return json([
                    'code' => 400,
                    'msg' => '请提供报告ID',
                    'data' => []
                ]);
            }
            Log::info('流式AI推荐请求参数: ' . json_encode(['report_id' => $reportId], JSON_UNESCAPED_UNICODE));
            // 构建上下文
            $context =  db("school_report")->where("id", $reportId)->value("context");
            if(empty($context)){
                return json([
                    'code' => 400,
                    'msg' => '请先生成报告',
                    'data' => []
                ]);
            }
            Log::info("context: " . json_encode($context, JSON_UNESCAPED_UNICODE));
            // 设置响应头，启用SSE
            $response = response('');
            $response->withHeaders([
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'X-Accel-Buffering' => 'no' // 禁用Nginx缓冲
            ]);

            $connection = $request->connection;
            $id = Timer::add(2, function () use ($connection, &$id, $context) {
                $connection->send(new ServerSentEvents(['data' => "start"]));
                $returnContext  = "";
                $sendContent = "";
                $this->streamQianwenApi($context, function($chunk) use ($connection,&$returnContext, &$sendContent) {
                    $sendContent .= $chunk;
                 if (preg_match('/[A-O]/', $sendContent, $matches, PREG_OFFSET_CAPTURE)) {
                    $charPosition = $matches[0][1]; // 获取匹配到的 A-O 字母的起始索引
                    $remainingString = substr($sendContent, $charPosition + 1);
                    if (mb_strlen($remainingString, 'UTF-8') < 5) {
                        return; 
                    } 
                }

                if(preg_match('/L\./', $sendContent) && strlen($sendContent) < 10){
                    return;
                }

                    log::info("流式输出: " .  $sendContent);
                    if($sendContent =='done'){
                        $connection->send(new ServerSentEvents([
                            'event' => 'end',
                            'data' => 'done']));
                        $returnContext  = null;
                        $sendContent = null;
                        return;
                    }
                    // 发送数据块
                    $connection->send(new ServerSentEvents(['data' => $sendContent]));
                    $sendContent = "";
                });

            }, [], false); 

            return $response;
        } catch (Exception $e) {
            Log::error('流式AI推荐异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 返回错误响应
            $response = response('');
            $response->withHeaders([
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache'
            ]);
            $response->withBody("data: {\"type\":\"error\",\"content\":\"生成失败: " . addslashes($e->getMessage()) . "\"}\n\n");

            return $response;
        }
    }

    /**
     * 构建学生基本信息上下文
     *
     * @param int $reportId 报告ID
     * @return array|bool|string
     */
    public function buildAiContext($reportId)
    {
        Log::info('院校数量: ' . $reportId);
        // 验证参数
        if (empty($reportId)) {
            return false;
        }
        try {
            // 1. 获取报告信息
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                return false;
            }

            // 2. 获取学生基本信息
            $student = Student::where('id', $report['student_id'])->where('is_delete', 0)->find();
            if (empty($student)) {
                return false;
            }
            
            // 3. 获取学生详细信息
            $studentDetail = StudentDetail::where('student_id', $report['student_id'])->find();
            $schoolLevel = $studentDetail['school_level'] ? explode(',', $studentDetail['school_level']) : [];
            $schools  = SchoolInfo::whereIn('province', $studentDetail['target_provinces'])->where(function($query) use ($schoolLevel) {
                              
                foreach ($schoolLevel as $value) {
                    if($value == "985"){
                        $query->whereOr("school_type","985,211,双一流");
                    }
                    if($value == "211"){
                        $query->whereOr("school_type","211,双一流");
                    }
                    if($value == "双一流"){
                        $query->whereOr("school_type","双一流");
                    }
                     if($value == "双非"){
                        $query->whereOr("school_type","");
                    }
                }
            })->where('major_code', 'in', explode(',', $report['major_code']))
            ->where('second_level_name', '!=', '采用一级学科')
            ->column("school_name,major_code,id, college, major_name");
            $schools = uniqueSchoolData($schools);

            Log::info('院校数量: ' . count($schools));
            $schoolIds = array_column($schools, 'id');
            $this->callSpiderApi($schoolIds);
            $schoolBasicInfo = SchoolBasicInfo::whereIn("school_id", $schoolIds)
            ->column('reference_books, retest_content', "school_id");
            $schoolsContext = "";
            $result =  YzwMajor::where(function ($query) use ($schools) {
            foreach ($schools as $index => $item) {
                if ($index === 0) {
                    // 第一个条件组
                    $query->where(function ($subQuery) use ($item) {
                        $subQuery->where('school_name', $item['school_name'])
                                ->where('major_code', $item['major_code']);
                    });
                } else {
                    // 后续条件组用 OR 连接
                    $query->whereOr(function ($subQuery) use ($item) {
                        $subQuery->where('school_name', $item['school_name'])
                                ->where('major_code', $item['major_code']);
                    });
                }
            }
        })
        ->field('school_name, major_code, department_name,exam_subject1,exam_subject2,exam_subject3,exam_subject4')
        ->select();


        $items = [];
        foreach($result as $value){
            $items[$value['school_name'].$value['department_name'].$value['major_code'] ] = $value;
        }
        $result = array_values($items);

        Log::info('院校数量: ' . count($result));
        $storeSchoolIds = [];
        foreach($result as $v){
          
            foreach($schools as $val){
                if($val['school_name'] == $v['school_name'] && $val['major_code'] == $v['major_code']&& substr($v['department_name'],0,8) == substr($val['college'],0,8)){
                    
                    $schoolsContext .= "学校名称: " . $v['school_name'] . "\n";
                    $schoolsContext .= "专业名称: " . $v['major_code'] . "\n";
                    $schoolsContext .= "学院名称: " . $v['department_name'] . "\n";
                    $schoolsContext .= "初试考试科目: " . $v['exam_subject1'] . ",".$v['exam_subject2'].", ".$v['exam_subject3'].", ".$v['exam_subject4'] . "\n";
                    $val['college'] = $v['department_name'];
                    $storeSchoolIds[] = $val;
                    $schoolsContext .= "初试参考书: ".$schoolBasicInfo[$val['id']]['reference_books']."\n";
                    $schoolsContext .= "复试内容: ".$schoolBasicInfo[$val['id']]['retest_content']."\n";
                    $schoolsContext .= "招生人数：".$v['planned_num']."\n";
                }
            }
        }
        Redis::setEx("report_".$reportId,3600, json_encode($storeSchoolIds));
        //Log::info('sql:'.YzwMajor::getLastSql());
            // 4. 构建学生基本信息上下文
            $context = "学生基本信息：\n";
            $context .= "姓名：" . $student['name'] . "\n";
            $context .= "性别：" . ($student['sex'] == 1 ? '男' : ($student['sex'] == 2 ? '女' : '其他')) . "\n";
            $context .= "本科院校：" . $student['undergraduate_school_name'] . "\n";
            $context .= "本科专业：" . $student['undergraduate_major_name'] . "\n";
            $context .= "培养方式：" . ($student['educational_style']=="0"?"全日制":"非全日制") . "\n";
            $context .= "是否跨专业：" . ($student['is_multi_disciplinary']==1 ? '是' : '否') . "\n";
            //$context .= "目标专业：" . $report['target_major'] . "（专业代码：" . $report['major_code'] . "）\n\n";

            // 5. 添加本科成绩信息（如果不为空）
            if (!empty($studentDetail) && !empty($studentDetail['undergraduate_transcript'])) {
                // 类型检查：如果是字符串则解码，如果已经是数组则直接使用
                if (is_string($studentDetail['undergraduate_transcript'])) {
                    $undergraduateTranscript = json_decode($studentDetail['undergraduate_transcript'], true);
                } else {
                    $undergraduateTranscript = $studentDetail['undergraduate_transcript'];
                }
                if (!empty($undergraduateTranscript) && is_array($undergraduateTranscript)) {
                    $context .= "本科成绩：\n";
                    foreach ($undergraduateTranscript as $course) {
                        if (!empty($course['title']) && !empty($course['score'])) {
                            $context .= $course['title'] . "：" . $course['score'] . "分\n";
                        }
                    }
                    $context .= "\n";
                }
            }

            // 6. 添加英语基础信息（如果不为空）
            if (!empty($studentDetail)) {
                $englishInfo = [];
                if (!empty($studentDetail['english_score'])) {
                    $englishInfo[] = "高考英语成绩：" . $studentDetail['english_score'] . "分";
                }
                if (!empty($studentDetail['cet4'])) {
                    $englishInfo[] = "大学四级成绩：" . $studentDetail['cet4'] . "分";
                }
                if (!empty($studentDetail['cet6'])) {
                    $englishInfo[] = "大学六级成绩：" . $studentDetail['cet6'] . "分";
                }
                if (!empty($studentDetail['tofel_score'])) {
                    $englishInfo[] = "托福成绩：" . $studentDetail['tofel_score'] . "分";
                }
                if (!empty($studentDetail['ielts_score'])) {
                    $englishInfo[] = "雅思成绩：" . $studentDetail['ielts_score'] . "分";
                }
                if (!empty($studentDetail['english_ability'])) {
                    $englishInfo[] = "英语能力：" . $studentDetail['english_ability'];
                }
                
                if (!empty($englishInfo)) {
                    $context .= "英语基础：\n";
                    foreach ($englishInfo as $info) {
                        $context .= $info . "\n";
                    }
                    $context .= "\n";
                }
            }

            $context .= "考试成绩预估：\n";
            // 字段映射说明：politics=政治，english_s=英语，english_type=业务课一，math_type=业务课二
            $context .= "政治：" . $report['politics_score'] . "分\n";
            $context .= "英语：" . $report['english_score'] . "分\n";
            $context .= "业务课一：" . $report['english_type'] . "分\n";
            if (!empty($report['math_type'])) {
                $context .= "业务课二：" . $report['math_type'] . "分\n";
            }
            $context .= "专业课：" . $report['professional_score'] . "分\n";
            $context .= "总分：" . $report['total_score'] . "分\n\n";

            $context .= "目标偏好：\n";
            $context .= "目标区域：" . $report['target_region'] . "\n";
            if (!empty($report['target_provinces'])) {
                $context .= "目标省份：" . $report['target_provinces'] . "\n";
            }
            // 院校层次原样输出，不做任何修改
            $context .= "院校层次：" . $report['school_level'] . "\n";
            if (!empty($report['dream_school'])) {
                $context .= "梦想院校：" . $report['dream_school'] . "\n";
            }
            if (!empty($report['personal_needs'])) {
                $context .= "个性化需求：" . $report['personal_needs'] . "\n";
            }
            if (!empty($report['weak_modules'])) {
                $context .= "薄弱模块：" . $report['weak_modules'] . "\n";
            }
            $context .= "学校列表：\n$schoolsContext\n";
            

        } catch (Exception $e) {
            Log::error('构建学生基本信息异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            throw $e;
        }
        return $context;
    }

    /**
     * 流式调用大语言模型接口
     *
     * @param string $context 上下文内容
     * @param callable $callback 回调函数，用于处理每个数据块
     * @return void
     */
    private function streamLargeLanguageModel($context, $callback)
    {

        $allContent = '';
        $url = "https://api.deepseek.com/chat/completions";
        $token = "sk-dd1a506a131b4ca798fbc435737b15c2";

        // 构建请求负载 - 使用流式输出
        $payload = [
            "model" => "deepseek-chat",
            "messages" => [
                [
                    "role" => "system",
                    "content" => "你是一位专业的考研规划师，擅长根据学生的情况和目标推荐最适合的院校
"
                ],
                [
                    "role" => "user",
                    "content" => $context
                ]
            ],
            "stream" => true, // 启用流式输出
            "temperature" => 0.7,
            "max_tokens" => 4000 // 减少token数量，避免超时
        ];

        try {
            // 记录请求信息，用于调试
            Log::info('流式 DeepSeek API 请求URL: ' . $url);
            Log::info('流式 DeepSeek API 请求负载: ' . json_encode($payload, JSON_UNESCAPED_UNICODE));

            // 初始化cURL
            $ch = curl_init($url);

            // 设置cURL选项
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                "Authorization: Bearer " . $token,
                "Content-Type: application/json",
                "Accept: text/event-stream"
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // 不返回响应
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 连接超时时间（秒）
            curl_setopt($ch, CURLOPT_TIMEOUT, 600); // 10分钟超时

            // 设置更长的超时时间，避免 15000ms 超时问题
            ini_set('max_execution_time', 600); // 设置PHP执行时间限制为10分钟

            // 设置写入回调函数
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use ($callback, &$allContent) {
                // 处理SSE数据
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    if (strpos($line, 'data: ') === 0) {
                        $jsonData = substr($line, 6); // 去掉 "data: " 前缀
                        if ($jsonData === '[DONE]') {
                            // 流结束
                            Log::info('流式响应结束:'.$allContent);
                            $callback('done');
                            continue;
                        }

                        try {
                            $decoded = json_decode($jsonData, true);
                            if (isset($decoded['choices'][0]['delta']['content'])) {
                                $content = $decoded['choices'][0]['delta']['content'];
                                // 调用回调函数处理内容
                                $allContent .= $content;
                                $callback($content);
                            }
                        } catch (\Exception $e) {
                            Log::error('解析流式响应失败: ' . $e->getMessage());
                        }
                    }
                }
                return strlen($data); // 必须返回数据长度
            });

            // 执行请求
            curl_exec($ch);

            // 检查错误
            if (curl_errno($ch)) {
                Log::error('流式 API 请求失败: ' . curl_error($ch));
                throw new \Exception('API请求失败: ' . curl_error($ch));
            }

            // 关闭cURL
            curl_close($ch);

        } catch (\Exception $e) {
            Log::error('流式大模型接口调用异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 整合学校数据库信息
     *
     * @param array $school 学校基本信息
     * @return array 整合后的学校信息
     */
    private function enrichSchoolData($school)
    {
        try {
            $schoolId = $school['school_id'];
            $schoolName = $school['school_name'];


            // 1. 查询拟录取名单统计
            $admissionStats = $this->getAdmissionStats($schoolId);
           

            // 2. 查询复试名单统计
            $retestStats = $this->getRetestStats($schoolId);
            

            // 3. 查询院校基本信息
            $schoolBasicInfo = $this->getSchoolBasicInfo($schoolId);
           

            // 4. 查询学校信息（logo及标签）
            $schoolInfo = $this->getSchoolInfo($schoolName);

            // 5. 获取本年度复试名单列表
            $currentYearRetestList = $this->getCurrentYearRetestList($schoolId);

            // 6. 获取本年度拟录取名单列表
            $currentYearAdmissionList = $this->getCurrentYearAdmissionList($schoolId);

            // 整合数据
            $school['admission_stats'] = $admissionStats;
            $school['retest_stats'] = $retestStats;
            $school['basic_info'] = $schoolBasicInfo;
            $school['school_info'] = $schoolInfo;
            $school['current_year_retest_list'] = $currentYearRetestList;
            $school['current_year_admission_list'] = $currentYearAdmissionList;

            return $school;

        } catch (\Exception $e) {
            Log::error('整合学校数据异常: ' . $e->getMessage());
            return $school; // 异常时返回原始数据
        }
    }

    /**
     * 获取拟录取名单统计
     *
     * @param int $schoolId
     * @return array
     */
    private function getAdmissionStats($schoolId)
    {
        try {
            // 使用原生SQL查询，获取拟录取统计信息
            $sql = "SELECT
                        COUNT(*) as total_admission,
                        AVG(CAST(initial_score AS DECIMAL(10,2))) as avg_initial_score,
                        MAX(CAST(initial_score AS DECIMAL(10,2))) as max_initial_score,
                        MIN(CAST(initial_score AS DECIMAL(10,2))) as min_initial_score,
                        AVG(CAST(retest_score AS DECIMAL(10,2))) as avg_retest_score,
                        MAX(CAST(retest_score AS DECIMAL(10,2))) as max_retest_score,
                        MIN(CAST(retest_score AS DECIMAL(10,2))) as min_retest_score,
                        year
                    FROM ba_admission_list
                    WHERE school_id = $schoolId
                    GROUP BY year
                    ORDER BY year DESC
                    LIMIT 3";

            $result = Db::query($sql);

            return [
                'yearly_stats' => $result ?: [],
                'has_data' => !empty($result)
            ];

        } catch (\Exception $e) {
            Log::error('获取拟录取统计异常: ' . $e->getMessage());
            return ['yearly_stats' => [], 'has_data' => false];
        }
    }

    /**
     * 获取复试名单统计
     *
     * @param int $schoolId
     * @return array
     */
    private function getRetestStats($schoolId)
    {
        try {
            // 使用原生SQL查询，获取复试统计信息
            $sql = "SELECT
                        COUNT(*) as total_retest,
                        AVG(initial_score) as avg_initial_score,
                        MAX(initial_score) as max_initial_score,
                        MIN(initial_score) as min_initial_score,
                        COUNT(CASE WHEN admission_status = '拟录取' THEN 1 END) as admission_count,
                        year
                    FROM ba_retest_list
                    WHERE school_id = $schoolId
                    GROUP BY year
                    ORDER BY year DESC
                    LIMIT 3";

            $result = Db::query($sql);

            // 计算录取率
            foreach ($result as &$item) {
                if ($item['total_retest'] > 0) {
                    $item['admission_rate'] = round(($item['admission_count'] / $item['total_retest']) * 100, 2);
                } else {
                    $item['admission_rate'] = 0;
                }
            }

            return [
                'yearly_stats' => $result ?: [],
                'has_data' => !empty($result)
            ];

        } catch (\Exception $e) {
            Log::error('获取复试统计异常: ' . $e->getMessage());
            return ['yearly_stats' => [], 'has_data' => false];
        }
    }

    /**
     * 获取院校基本信息
     *
     * @param int $schoolId
     * @return array
     */
    private function getSchoolBasicInfo($schoolId)
    {
        try {
            $result = Db::name('school_basic_info')
                ->where('school_id', $schoolId)
                ->field('research_direction,exam_range,reference_books,retest_content,tuition_fee,study_years,accommodation,admission_requirements')
                ->find();

            if ($result && is_object($result) && method_exists($result, 'toArray')) {
               // Log::info("找到学校基本信息 (ID: {$schoolId}): " . json_encode($result->toArray(), JSON_UNESCAPED_UNICODE));
                return $result->toArray();
            } elseif (is_array($result)) {
               // Log::info("找到学校基本信息 (ID: {$schoolId}): " . json_encode($result, JSON_UNESCAPED_UNICODE));
                return $result;
            } else {
                Log::warning("未找到学校基本信息 (ID: {$schoolId})");
                return [];
            }

        } catch (\Exception $e) {
            Log::error('获取院校基本信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取学校信息（logo及标签）
     *
     * @param string $schoolName
     * @return array
     */
    private function getSchoolInfo($schoolName)
    {
        try {
            $result = Db::name('school')
                ->where('name', $schoolName)
                ->where('is_delete', 0)
                ->field('name, id,logo,tag_211,tag_985,dual_class,address,phone,home_site,zsb_site')
                ->find();

            if ($result) {
                Log::info("找到学校数据: {$result['name']}, ID: {$result['id']}, logo: {$result['logo']}");
            } else {
                Log::warning("未找到学校信息: {$schoolName}");
                // 尝试模糊匹配
                $fuzzyResult = Db::name('school')
                    ->where('name', 'like', "%{$schoolName}%")
                    ->where('is_delete', 0)
                    ->field('name, id,logo,tag_211,tag_985,dual_class,address,phone,home_site,zsb_site')
                    ->find();

                if ($fuzzyResult) {
                    Log::info("通过模糊匹配找到学校: {$fuzzyResult['name']} (搜索: {$schoolName})");
                    $result = $fuzzyResult;
                } else {
                    Log::error("完全未找到学校信息: {$schoolName}");
                    return [];
                }
            }

            if ($result) {
                // 检查是否为对象，如果是则转换为数组
                if (is_object($result) && method_exists($result, 'toArray')) {
                    $schoolData = $result->toArray();
                } elseif (is_array($result)) {
                    $schoolData = $result;
                } else {
                    return [];
                }

                // 处理logo
                if (empty($schoolData['logo'])) {

                    $schoolData['logo'] = "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/{$schoolData['id']}.jpeg";
                }

                // 转换标签为布尔值
                $schoolData['is_211'] = (bool)$schoolData['tag_211'];
                $schoolData['is_985'] = (bool)$schoolData['tag_985'];
                $schoolData['is_dual_class'] = (bool)$schoolData['dual_class'];

                return $schoolData;
            }

            return [];

        } catch (\Exception $e) {
            Log::error('获取学校信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取本年度复试名单列表
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearRetestList($schoolId)
    {
        try {
            // 首先尝试获取当前年度的数据
            $currentYear = intval(date('Y'));

            $resultCollection = Db::name('retest_list')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name,college,major_code,major_name,politics_score,english_score,major1_score,major2_score,initial_score,volunteer_type,admission_status,year')
                ->order('initial_score', 'desc')
                ->limit(20)
                ->select();

            $result = [];
            if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                $result = $resultCollection->toArray();
            } elseif (is_array($resultCollection)) {
                $result = $resultCollection;
            }

            // 如果当前年度没有数据，获取最新年度的数据
            if (empty($result)) {
                $maxYearResult = Db::name('retest_list')
                    ->where('school_id', $schoolId)
                    ->max('year');

                if ($maxYearResult) {
                    $resultCollection = Db::name('retest_list')
                        ->where('school_id', $schoolId)
                        ->where('year', $maxYearResult)
                        ->field('name,college,major_code,major_name,politics_score,english_score,major1_score,major2_score,initial_score,volunteer_type,admission_status,year')
                        ->order('initial_score', 'desc')
                        ->limit(20)
                        ->select();

                    if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                        $result = $resultCollection->toArray();
                    } elseif (is_array($resultCollection)) {
                        $result = $resultCollection;
                    }
                }
            }

            return [
                'list' => $result ?: [],
                'year' => !empty($result) ? $result[0]['year'] : null,
                'count' => count($result ?: [])
            ];

        } catch (\Exception $e) {
            Log::error('获取本年度复试名单列表异常: ' . $e->getMessage());
            return ['list' => [], 'year' => null, 'count' => 0];
        }
    }

    /**
     * 获取本年度拟录取名单列表
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearAdmissionList($schoolId)
    {
        try {
            // 首先尝试获取当前年度的数据
            $currentYear = intval(date('Y'));

            $resultCollection = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name,college,major_code,major_name,initial_score,retest_score,total_score,first_choice_school,student_remark,year')
                ->order('total_score', 'desc')
                ->limit(20)
                ->select();

            $result = [];
            if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                $result = $resultCollection->toArray();
            } elseif (is_array($resultCollection)) {
                $result = $resultCollection;
            }

            // 如果当前年度没有数据，获取最新年度的数据
            if (empty($result)) {
                $maxYearResult = Db::name('admission_list')
                    ->where('school_id', $schoolId)
                    ->max('year');

                if ($maxYearResult) {
                    $resultCollection = Db::name('admission_list')
                        ->where('school_id', $schoolId)
                        ->where('year', $maxYearResult)
                        ->field('name,college,major_code,major_name,initial_score,retest_score,total_score,first_choice_school,student_remark,year')
                        ->order('total_score', 'desc')
                        ->limit(20)
                        ->select();

                    if ($resultCollection && method_exists($resultCollection, 'toArray')) {
                        $result = $resultCollection->toArray();
                    } elseif (is_array($resultCollection)) {
                        $result = $resultCollection;
                    }
                }
            }

            return [
                'list' => $result ?: [],
                'year' => !empty($result) ? $result[0]['year'] : null,
                'count' => count($result ?: [])
            ];

        } catch (\Exception $e) {
            Log::error('获取本年度拟录取名单列表异常: ' . $e->getMessage());
            return ['list' => [], 'year' => null, 'count' => 0];
        }
    }

    /**
     * 获取院校列表数据
     *
     * @param int $reportId
     * @return array
     */
    private function getSchoolListData($reportId)
    {
        try {
            // 1. 获取报告信息
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                return [];
            }

            // 2. 构建查询条件，查询符合条件的学校
            $totalScore = intval($report['total_score']);
            $majorCode = $report['major_code'];
            $targetRegion = $report['target_region'];
            $targetProvinces = $report['target_provinces'];
            $schoolLevel = $report['school_level'];

            // 构建查询条件
            $query = SchoolInfo::where('must_reach_score', '<=', $totalScore+20);

            // 如果有专业代码，添加专业代码条件
            if (!empty($majorCode)) {
                $query = $query->where('major_code', $majorCode);
            }

            // 如果有省份条件，添加省份条件
            if (!empty($targetProvinces)) {
                $provinceArray = explode(',', $targetProvinces);
                if (!empty($provinceArray)) {
                    $query = $query->whereIn('province', $provinceArray);
                }
            }

            if (!empty($schoolLevel)) {
                if($schoolLevel == "985"){
                    $schoolLevel = "985,211,双一流";
                }
                if($schoolLevel == "211"){
                    $schoolLevel = "211,双一流";
                }
                if($schoolLevel == "双一流"){
                    $schoolLevel = "双一流";
                }
                $query = $query->where("school_type", $schoolLevel);
            }

            // 按必达分排序（从高到低）
            $query = $query->order('must_reach_score', 'desc');

            // 获取数据，限制30条用于列表展示
            $schoolsCollection = $query->limit(30)->select();
            $schools = [];
            if ($schoolsCollection && method_exists($schoolsCollection, 'toArray')) {
                $schools = $schoolsCollection->toArray();
            } elseif (is_array($schoolsCollection)) {
                $schools = $schoolsCollection;
            }

            // 如果找不到足够的学校，放宽条件再次查询
            if (count($schools) < 20) {
                $query = SchoolInfo::where('must_reach_score', '<=', $totalScore + 20);

                if (!empty($majorCode)) {
                    $query = $query->where('major_code', $majorCode);
                }
                if (!empty($targetProvinces)) {
                    $provinceArray = explode(',', $targetProvinces);
                    if (!empty($provinceArray)) {
                        $query = $query->whereIn('province', $provinceArray);
                    }
                }

                $query = $query->order('must_reach_score', 'desc');

                $additionalSchoolsCollection = $query->limit(30 - count($schools))->select();
                $additionalSchools = [];
                if ($additionalSchoolsCollection && method_exists($additionalSchoolsCollection, 'toArray')) {
                    $additionalSchools = $additionalSchoolsCollection->toArray();
                } elseif (is_array($additionalSchoolsCollection)) {
                    $additionalSchools = $additionalSchoolsCollection;
                }
                $schools = array_merge($schools, $additionalSchools);
            }

            // 3. 整理学校列表数据
            $schoolList = [];
            $schoolNames = []; // 用于记录已添加的学校名称，避免重复
            $index = 1;

            foreach ($schools as $school) {
                // 检查学校是否已经添加过
                if (in_array($school['school_name'], $schoolNames)) {
                    continue; // 如果已添加过，跳过当前学校
                }

                // 计算分差
                $scoreDiff = $totalScore - $school['must_reach_score'];

                // 确定地区
                $region = $this->getRegionByProvince($school['province']);

                // 确定城市（从省份中提取或使用area字段）
                $city = $school['area'] ?? $school['province'];

                // 处理学院信息
                $college = $school['college'] ?? '信息科学与技术学院';

                // 处理学校标签
                $tags = [];
                if (strpos($school['school_type'], '985') !== false) {
                    $tags[] = '985';
                }
                if (strpos($school['school_type'], '211') !== false) {
                    $tags[] = '211';
                }
                if (strpos($school['school_type'], '双一流') !== false) {
                    $tags[] = '双一流';
                }

                // 添加学校到列表
                $schoolList[] = [
                    'id' => $index,
                    'school_name' => $school['school_name'],
                    'region' => $region,
                    'city' => $city,
                    'college' => $college,
                    'major_name' => $school['major_name'],
                    'major_code' => $school['major_code'] ?? $majorCode,
                    'min_score' => $school['must_reach_score'],
                    'score_diff' => $scoreDiff,
                    'tags' => $tags,
                    'tag_985' => in_array('985', $tags),
                    'tag_211' => in_array('211', $tags),
                    'tag_double' => in_array('双一流', $tags)
                ];

                // 记录已添加的学校名称
                $schoolNames[] = $school['school_name'];
                $index++;

                // 限制最多返回20所学校
                if (count($schoolList) >= 20) {
                    break;
                }
            }

            return $schoolList;

        } catch (\Exception $e) {
            Log::error('获取院校列表数据异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据省份获取地区
     *
     * @param string $province
     * @return string
     */
    private function getRegionByProvince($province)
    {
        $regionMap = [
            '华北' => ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区'],
            '东北' => ['辽宁省', '吉林省', '黑龙江省'],
            '华东' => ['上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省'],
            '华中' => ['河南省', '湖北省', '湖南省'],
            '华南' => ['广东省', '广西壮族自治区', '海南省'],
            '西南' => ['重庆市', '四川省', '贵州省', '云南省', '西藏自治区'],
            '西北' => ['陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区']
        ];

        foreach ($regionMap as $region => $provinces) {
            if (in_array($province, $provinces)) {
                return $region;
            }
        }

        return '其他';
    }

    /**
     * 将推荐院校数据写入ba_report_info表
     *
     * @param array $recommendedSchools 推荐院校数据
     * @param int $reportId 报告ID
     * @return void
     */
    private function saveRecommendedSchoolsToDatabase($recommendedSchools, $reportId)
    {
        try {
            // 获取报告信息以获取学生ID
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                Log::error('未找到报告信息，无法保存推荐院校数据，reportId: ' . $reportId);
                return;
            }

            $studentId = $report['student_id'];
            $currentTime = time();

            Log::info('开始保存推荐院校数据到数据库，reportId: ' . $reportId . ', studentId: ' . $studentId);

            // 先删除该报告的旧推荐数据
            ReportInfo::deleteByReportId($reportId);
            Log::info('已删除报告ID ' . $reportId . ' 的旧推荐数据');

            $insertData = [];

            // 处理推荐列表
            if (isset($recommendedSchools['recommend_list']) && is_array($recommendedSchools['recommend_list'])) {
                foreach ($recommendedSchools['recommend_list'] as $school) {
                    $schoolId = isset($this->cacheSchoolIds[$school['school_name']]) ? $this->cacheSchoolIds[$school['school_name']] : 0;

                    $insertData[] = [
                        'student_id' => $studentId,
                        'school_id' => $schoolId,
                        'report_id' => $reportId,
                        'school_name' => $school['school_name'],
                        'is_high_recommend' => 0, // 普通推荐
                        'competition_difficulty' => $school['difficulty_analysis'] ?? '',
                        'suggestions' => $school['suggest'] ?? '',
                        'reason_recommendation' => $school['reason'] ?? '',
                        'status' => 1,
                        'createtime' => $currentTime,
                        'updatetime' => $currentTime
                    ];
                }
            }

            // 处理高推荐列表
            if (isset($recommendedSchools['high_recommend_list']) && is_array($recommendedSchools['high_recommend_list'])) {
                foreach ($recommendedSchools['high_recommend_list'] as $school) {
                    $schoolId = isset($this->cacheSchoolIds[$school['school_name']]) ? $this->cacheSchoolIds[$school['school_name']] : 0;

                    $insertData[] = [
                        'student_id' => $studentId,
                        'school_id' => $schoolId,
                        'report_id' => $reportId,
                        'school_name' => $school['school_name'],
                        'is_high_recommend' => 1, // 高推荐
                        'competition_difficulty' => '', // 高推荐列表可能没有详细分析
                        'suggestions' => '',
                        'reason_recommendation' => $school['reason'] ?? '',
                        'status' => 1,
                        'createtime' => $currentTime,
                        'updatetime' => $currentTime
                    ];
                }
            }

            // 批量插入数据
            if (!empty($insertData)) {
                $result = ReportInfo::insertRecommendations($insertData);
                if ($result) {
                    Log::info('推荐院校数据保存完成，共插入 ' . count($insertData) . ' 条记录');
                } else {
                    Log::error('批量插入推荐院校数据失败');
                }
            } else {
                Log::warning('没有推荐院校数据需要插入');
            }

        } catch (\Exception $e) {
            Log::error('保存推荐院校数据到数据库异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
        }
    }

    /**
     * 测试推荐院校数据写入功能
     *
     * @param Request $request
     * @return \support\Response
     */
    public function testSaveRecommendations(Request $request)
    {
        try {
            // 模拟推荐院校数据
            $testRecommendedSchools = [
                'recommend_list' => [
                    [
                        'school_name' => '中国科学技术大学',
                        'major_name' => '计算机科学与技术',
                        'difficulty_analysis' => '中国科学技术大学计算机科学与技术专业竞争非常激烈，属于国内顶尖水平。每年报考人数众多，录取分数线较高，通常在360分以上。由于该校在计算机领域的声誉和科研实力，吸引了大量优秀考生。',
                        'suggest' => '备考目标建议政治75分，英语85分，数学100分，专业课120分，总分380分以上。重点加强数学和专业课的复习，尤其是算法和数据结构部分。',
                        'reason' => '推荐原因：中国科学技术大学是张三的梦想院校，且该校计算机科学与技术专业在国内排名靠前，科研实力雄厚。虽然竞争激烈，但张三的预估分数接近录取线，通过努力有可能达到要求。'
                    ],
                    [
                        'school_name' => '浙江大学',
                        'major_name' => '计算机科学与技术',
                        'difficulty_analysis' => '浙江大学计算机科学与技术专业竞争极为激烈，录取分数线通常在370分以上。该校计算机学科评估为A+，吸引了大量顶尖考生。复试环节也非常严格，对综合素质要求高。',
                        'suggest' => '备考目标建议政治75分，英语85分，数学105分，专业课125分，总分390分以上。重点提升数学和专业课成绩，同时注重编程能力的提高。',
                        'reason' => '推荐原因：浙江大学计算机科学与技术专业在国内享有极高声誉，科研和就业前景都非常好。虽然竞争激烈，但张三的预估分数显示他有潜力通过努力达到要求。'
                    ]
                ],
                'high_recommend_list' => [
                    [
                        'school_name' => '华中科技大学',
                        'major_name' => '计算机科学与技术',
                        'reason' => '华中科技大学计算机专业实力强劲，性价比较高，适合作为稳妥选择。'
                    ]
                ]
            ];

            // 使用测试报告ID（假设存在ID为8的报告）
            $testReportId = 8;

            // 设置缓存的学校ID（模拟）
            $this->cacheSchoolIds = [
                '中国科学技术大学' => 1,
                '浙江大学' => 2,
                '华中科技大学' => 3
            ];

            // 调用保存方法
            $this->saveRecommendedSchoolsToDatabase($testRecommendedSchools, $testReportId);

            // 查询保存的数据进行验证
            $savedData = ReportInfo::getRecommendationsByReportId($testReportId);

            return json([
                'code' => 0,
                'msg' => '测试成功',
                'data' => [
                    'test_data' => $testRecommendedSchools,
                    'saved_count' => count($savedData),
                    'saved_data' => $savedData
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('测试推荐院校数据写入异常: ' . $e->getMessage());
            return json([
                'code' => 1,
                'msg' => '测试失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取单个学校的详细信息
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolDetail(Request $request)
    {
        try {
            $params = $request->all();

            // 1. 验证必需参数
            if (empty($params['school_name'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校名称不能为空',
                    'data' => null
                ]);
            }

            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            //移除
            $schoolName =   trim($params['school_name']);
            $reportId =  trim($params['report_id']);

            // 从ba_school_report表中查找学校ID
            $reportSchool = SchoolReport::where('id', $reportId)
                ->where('is_delete', 0)
                ->find();
            if (!empty($reportSchool)) {
    
                // 根据学校名称在ba_school_info表中查找school_id
                $schoolInfo = SchoolInfo::where('school_name', $schoolName)
                    ->where('major_code', $reportSchool['major_code'])
                    ->find();
                Log::info('schoolInfoSql: ' . db('ba_school_info')->getLastSql());
                if (!empty($schoolInfo)) {
                    $schoolId = $schoolInfo['id']; // 使用id字段而不是school_id字段
                    
                    // 直接构建学校信息并丰富数据
                    $school = [
                        'school_name' => $schoolName,
                        'school_id' => $schoolId
                    ];
                    
                    // 调用enrichSchoolData方法获取详细信息
                    $enrichedSchool = $this->enrichSchoolData($school);
                    
                    // 过滤掉已在前端流式处理的基本信息，只保留数据库相关的信息
                    $filteredInfo = [
                        'school_name' => $schoolName,
                        'school_id' => $schoolId,
                        // 只保留前端无法获取的数据
                        'admission_stats' => $enrichedSchool['admission_stats'] ?? [],
                        'retest_stats' => $enrichedSchool['retest_stats'] ?? [],
                        'school_info' => $enrichedSchool['school_info'] ?? [],
                        'current_year_retest_list' => $enrichedSchool['current_year_retest_list'] ?? [],
                        'current_year_admission_list' => $enrichedSchool['current_year_admission_list'] ?? [],
                        // 不包含basic_info，因为这些信息已在前端流式处理
                    ];
                    
                    $min_score = 0;
                    
                    if(is_array($enrichedSchool['current_year_retest_list']["list"]) &&
                    isset($enrichedSchool['current_year_retest_list']['list'][0])&&
                    isset($enrichedSchool['current_year_retest_list']['list'][0]['initial_score'])
                    ){
                        $min_score = $enrichedSchool['current_year_retest_list']['list'][0]['initial_score'];
                    }
                    
                    // 构建SchoolListItem结构的数据
                    $schoolListItem = [
                        'id' => (int)$schoolId,
                        'school_name' => $schoolName,
                        'region' => $this->getRegionByProvince($enrichedSchool['school_info']['address'] ?? '未知'),
                        'city' => $this->extractCityFromAddress($enrichedSchool['school_info']['address'] ?? '未知'),
                        'college' => $schoolInfo['college'] ?? '', // 默认学院，可根据实际情况调整
                        'major_name' => $reportSchool['target_major'] ?? '',
                        'major_code' => $reportSchool['major_code'] ?? '', // 从报告中获取专业代码
                        'min_score' => $min_score,
                        'score_diff' => $reportSchool['total_score'] - $min_score,
                        'tags' => $this->buildSchoolTags($enrichedSchool['school_info'] ?? []),
                        'tag_985' => (bool)($enrichedSchool['school_info']['tag_985'] ?? false),
                        'tag_211' => (bool)($enrichedSchool['school_info']['tag_211'] ?? false),
                        'tag_double' => (bool)($enrichedSchool['school_info']['dual_class'] ?? false)
                    ];
                    
                    // 获取该学校该专业的分数线
                    if ($schoolInfo) {
                        $schoolListItem['min_score'] = (int)($schoolInfo['must_reach_score'] ?? 0);
                        $schoolListItem['score_diff'] = (int)($reportSchool['total_score'] ?? 0) - $schoolListItem['min_score'];
                    }

                    // 获取招生情况数据
                    $admissionData = $this->getAdmissionData($schoolName, $reportSchool['major_code'], $schoolId);

                    // 返回结果 - 只返回前端无法获取的数据
                    return json([
                        'code' => 0,
                        'msg' => 'success',
                        'data' => [
                            'info' => $filteredInfo,
                            'item' => $schoolListItem,
                            'admission_data' => $admissionData
                        ]
                    ]);
                }
            }
            
            return json([
                'code' => 1,
                'msg' => '未找到该学校的信息',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取学校详细信息异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '获取学校详细信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取招生情况数据
     *
     * @param string $schoolName 学校名称
     * @param string $majorCode 专业代码
     * @param int $schoolId 学校ID
     * @return array
     */
    private function getAdmissionData($schoolName, $majorCode, $schoolId)
    {
        try {
            $admissionData = [];

            // 只获取最近一年的数据
            $currentYear = intval(date('Y'));
            $yearData = [
                'year' => $currentYear,
                'planCount' => 0,
                'examCount' => 0,
                'admitCount' => 0,
                'ratio' => '0%',
                'studentCount' => 0,
                'highestScore' => 0,
                'lowestScore' => 0,
                'averageScore' => 0
            ];

            // 1. 获取招生计划数据 - 按planned_num由高到低排序取第一条
            $planData = Db::name('yzw_major')
                ->where('school_name', $schoolName)
                ->where('major_code', $majorCode)
                ->order('planned_num', 'desc')
                ->value('planned_num');

            if ($planData) {
                $yearData['planCount'] = intval($planData);
            }

            // 2. 从ba_school_info的admission字段获取录取数据
            $schoolInfo = Db::name('school_info')
                ->where('school_name', $schoolName)
                ->where('major_code', $majorCode)
                ->value('admission');

            if ($schoolInfo && $this->parseAdmissionData($schoolInfo, $yearData)) {
                // 成功解析admission字段
            } else {
                // 3. 如果admission字段不符合格式，从其他表计算
                $this->calculateAdmissionFromTables($schoolId, $currentYear, $yearData);
            }

            // 4. 计算分数统计
            $this->calculateScoreStats($schoolId, $currentYear, $yearData);

            // 只有当年有数据时才添加到结果中
            if ($yearData['planCount'] > 0 || $yearData['admitCount'] > 0) {
                $admissionData[] = $yearData;
            }

            return $admissionData;

        } catch (\Exception $e) {
            Log::error('获取招生情况数据异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 解析admission字段数据
     *
     * @param string $admissionStr admission字段内容
     * @param array &$yearData 年度数据引用
     * @return bool 是否成功解析
     */
    private function parseAdmissionData($admissionStr, &$yearData)
    {
        try {
            // 匹配格式："总录:11  一志愿:11   调剂:0  必达分:405"
            if (preg_match('/总录:(\d+).*?一志愿:(\d+).*?调剂:(\d+)/', $admissionStr, $matches)) {
                $yearData['admitCount'] = intval($matches[1]);
                $yearData['examCount'] = intval($matches[2]);
                $yearData['studentCount'] = intval($matches[3]);

                // 计算一志愿录取比
                if ($yearData['admitCount'] > 0) {
                    $ratio = ($yearData['examCount'] / $yearData['admitCount']) * 100;
                    $yearData['ratio'] = number_format($ratio, 1) . '%';
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('解析admission数据异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 从其他表计算录取数据
     *
     * @param int $schoolId 学校ID
     * @param int $year 年份
     * @param array &$yearData 年度数据引用
     */
    private function calculateAdmissionFromTables($schoolId, $year, &$yearData)
    {
        try {
            // 从ba_admission_list计算录取数据
            $admissionStats = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->field('COUNT(*) as total_count,
                        SUM(CASE WHEN first_choice_school = (SELECT school_name FROM ba_school_info WHERE id = ' . $schoolId . ' LIMIT 1) THEN 1 ELSE 0 END) as first_choice_count')
                ->find();

            if ($admissionStats) {
                $yearData['admitCount'] = intval($admissionStats['total_count']);
                $yearData['examCount'] = intval($admissionStats['first_choice_count']);
                $yearData['studentCount'] = $yearData['admitCount'] - $yearData['examCount'];

                // 计算一志愿录取比
                if ($yearData['admitCount'] > 0) {
                    $ratio = ($yearData['examCount'] / $yearData['admitCount']) * 100;
                    $yearData['ratio'] = number_format($ratio, 1) . '%';
                }
            }

            // 从ba_retest_list获取复试人数（如果需要的话）
            $retestCount = Db::name('retest_list')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->count();

            if ($retestCount > 0 && $yearData['examCount'] == 0) {
                $yearData['examCount'] = $retestCount;
            }

        } catch (\Exception $e) {
            Log::error('从其他表计算录取数据异常: ' . $e->getMessage());
        }
    }

    /**
     * 计算分数统计
     *
     * @param int $schoolId 学校ID
     * @param int $year 年份
     * @param array &$yearData 年度数据引用
     */
    private function calculateScoreStats($schoolId, $year, &$yearData)
    {
        try {
            // 从ba_admission_list计算分数统计
            $scoreStats = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->field('MAX(CAST(initial_score AS DECIMAL(10,2))) as max_score,
                        MIN(CAST(initial_score AS DECIMAL(10,2))) as min_score,
                        AVG(CAST(initial_score AS DECIMAL(10,2))) as avg_score')
                ->find();

            if ($scoreStats) {
                $yearData['highestScore'] = intval($scoreStats['max_score'] ?? 0);
                $yearData['lowestScore'] = intval($scoreStats['min_score'] ?? 0);
                $yearData['averageScore'] = intval($scoreStats['avg_score'] ?? 0);
            }

        } catch (\Exception $e) {
            Log::error('计算分数统计异常: ' . $e->getMessage());
        }
    }

    /**
     * 构建学校标签数组
     *
     * @param array $schoolInfo
     * @return array
     */
    private function buildSchoolTags($schoolInfo)
    {
        $tags = [];
        if (!empty($schoolInfo['tag_985'])) {
            $tags[] = '985';
        }
        if (!empty($schoolInfo['tag_211'])) {
            $tags[] = '211';
        }
        if (!empty($schoolInfo['dual_class'])) {
            $tags[] = '双一流';
        }
        return $tags;
    }

    /**
     * 从地址中提取城市名称
     *
     * @param string $address
     * @return string
     */
    private function extractCityFromAddress($address)
    {
        // 简单的城市提取逻辑，可以根据需要优化
        if (preg_match('/(.+?省)?(.+?市)/', $address, $matches)) {
            return $matches[2] ?? '未知';
        }
        return '未知';
    }

    /**
     * 保存报告数据到ba_report_info表
     * 
     * @param Request $request
     * @return \support\Response
     */
    public function saveReportData(Request $request)
    {
        try {
            $params = $request->all();
            
            // 1. 验证必需参数
            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }
            
            if (empty($params['school_list']) || !is_array($params['school_list'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校列表不能为空',
                    'data' => null
                ]);
            }

            //获取报告信息
            $report = SchoolReport::where('id', $params['report_id'])->find()->toArray();
            if (empty($report)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到对应的报告信息',
                    'data' => null
                ]);
            }
            
            $studentId = $report['student_id'];
            $reportId = $params['report_id'];
            $schoolList = $params['school_list'];

            // $heighRecommendKey =  array_key_last($schoolList);
            // $recommendInfoArr = explode(",", $schoolList[$heighRecommendKey ]["school_name"]);
            // $schoolNameList = array_column($schoolList, 'school_name');

            // $schoolIds = array_column($schoolList, 'school_id');
            // $schoolInfo = SchoolInfo::whereIn("id", $schoolIds)
            // ->whereIn("school_name", $schoolNameList)
            // ->column("id", "school_name");
            // if (empty($schoolInfo)) {
            //     return json([
            //         'code' => 1,
            //         'msg' => '未找到对应的学校信息',
            //         'data' => null
            //     ]);
            // }
            // 2. 根据report_id查询student_id
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到对应的报告信息',
                    'data' => null
                ]);
            }
            
            // 4. 批量插入新数据
            $insertData = [];
            $currentTime = time();

            
            foreach ($schoolList as $school) {
                if(empty($school['school_name']) && $school['is_high_recommend'] ==1){
                    $arrtmp = explode("：", $school['reason'], 2);
                    if(isset($arrtmp[1])){
                       $schoolArr = explode("，", $arrtmp[1]);
                       $school['school_name'] = $schoolArr[0];
                        foreach($schoolList as $key => $value){
                            if($value['school_name'] == $school['school_name'] && $value['school_id'] != 0){
                                $school['school_id'] = $value['school_id'];
                                break;
                            }
                    }
                    }

                }
                $insertData[] = [
                    'student_id' => $studentId,
                    'school_id' => $school['school_id'] ?? 0,
                    'report_id' => $reportId,
                    'school_name' => $school['school_name'] ?? '',
                    'is_high_recommend' => $school['is_high_recommend'] ?? 0,
                    'competition_difficulty' => $school['difficulty_analysis'] ?? '',
                    'suggestions' => $school['suggest'] ?? '',
                    'reason_recommendation' => $school['reason'] ?? '',
                    'score_formula' => $school['score_formula'] ?? '',
                    'study_years' => $school['study_years'] ?? '',
                    'tuition_fee' => $school['tuition_fee'] ?? '',
                    'exam_subjects' => $school['exam_subjects'] ?? '',
                    'reference_books' => $school['reference_books'] ?? '',
                    'retest_content' => $school['retest_content'] ?? '',
                    'high_recommend_reason' => $school['high_recommend_reason'] ?? '',
                    'admission_requirements' => $school['admission_requirements'] ?? '',
                    'retest_score_requirement' => $school['retest_score_requirement'] ?? '',
                    'status' => 1,
                    'createtime' => $currentTime
                ];
            }
            Db::startTrans();
            // 5. 执行批量插入
            if (!empty($insertData)) {
                $result = ReportInfo::insertAll($insertData);
                $updateTimes =  User::where('id', $request->user->uid)->setInc('generated_report_times');
                if ($result && $updateTimes) {
                    Db::commit();
                    Log::info('报告数据保存成功，报告ID: ' . $reportId . '，共插入 ' . count($insertData) . ' 条记录');
                    return json([
                        'code' => 0,
                        'msg' => '报告数据保存成功',
                        'data' => [
                            'report_id' => $reportId,
                            'saved_count' => count($insertData)
                        ]
                    ]);
                } else {
                    throw new \Exception('批量插入数据失败');
                }
            } else {
                return json([
                    'code' => 1,
                    'msg' => '没有有效的学校数据需要保存',
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存报告数据异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '保存报告数据失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 保存编辑字段到ba_report_info表
     *
     * @param Request $request
     * @return \support\Response
     */
    public function saveReportFieldEdit(Request $request)
    {
        try {
            $params = $request->all();

            // 1. 验证必需参数
            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            if ( $params['is_high_recommend'] == 0 && empty($params['school_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校ID不能为空',
                    'data' => null
                ]);
            }

            if (empty($params['field_name'])) {
                return json([
                    'code' => 1,
                    'msg' => '字段名不能为空',
                    'data' => null
                ]);
            }

            $reportId = $params['report_id'];
            $schoolId = $params['school_id'];
            $fieldName = $params['field_name'];
            $fieldValue = $params['field_value'] ?? '';
            $isHighRecommend = $params['is_high_recommend'] ?? 0;

            Log::info('保存编辑字段', [
                'report_id' => $reportId,
                'school_id' => $schoolId,
                'field_name' => $fieldName,
                'field_value' => $fieldValue,
                'is_high_recommend' => $isHighRecommend
            ]);

            // 2. 开始事务
            Db::startTrans();

            // 3. 准备更新数据
            $updateData = [
                'updatetime' => time()
            ];

            // 根据字段名设置对应的数据库字段
            switch ($fieldName) {
                case 'score_formula':
                    $updateData['score_formula'] = $fieldValue;
                    break;
                case 'study_years':
                    $updateData['study_years'] = $fieldValue;
                    break;
                case 'tuition_fee':
                    $updateData['tuition_fee'] = $fieldValue;
                    break;
                case 'exam_subjects':
                    $updateData['exam_subjects'] = $fieldValue;
                    break;
                case 'reference_books':
                    $updateData['reference_books'] = $fieldValue;
                    break;
                case 'retest_content':
                    $updateData['retest_content'] = $fieldValue;
                    break;
                case 'retest_score_requirement':
                    $updateData['retest_score_requirement'] = $fieldValue;
                    break;
                case 'admission_requirements':
                    $updateData['admission_requirements'] = $fieldValue;
                    break;
                case 'competition_difficulty':
                    $updateData['competition_difficulty'] = $fieldValue;
                    break;
                case 'suggestions':
                    $updateData['suggestions'] = $fieldValue;
                    break;
                case 'high_recommend_reason':
                    // 高性价比院校推荐原因，更新reason_recommendation字段
                    if ($isHighRecommend == 1) {
                        $updateData['reason_recommendation'] = $fieldValue;
                        $updateData['high_recommend_reason'] = $fieldValue;
                    } else {
                        return json([
                            'code' => 1,
                            'msg' => '该院校不是高性价比推荐院校',
                            'data' => null
                        ]);
                    }
                    break;
                default:
                    return json([
                        'code' => 1,
                        'msg' => '不支持的字段名: ' . $fieldName,
                        'data' => null
                    ]);
            }
            // 4. 执行更新操作
            if ($fieldName == 'high_recommend_reason' && $isHighRecommend == 1) {
                // 高性价比院校特殊处理：更新所有is_high_recommend=1的记录
                $result = ReportInfo::where('report_id', $reportId)
                    ->where('is_high_recommend', 1)
                    ->update($updateData);
            } else {
                // 普通院校：根据report_id和school_id更新
                $result = ReportInfo::where('report_id', $reportId)
                    ->where('school_id', $schoolId)
                    ->update($updateData);
            }

            if ($result !== false) {
                // 提交事务
                Db::commit();

                Log::info('编辑字段保存成功', [
                    'report_id' => $reportId,
                    'school_id' => $schoolId,
                    'field_name' => $fieldName,
                    'affected_rows' => $result,
                    'is_high_recommend' => $isHighRecommend
                ]);

                return json([
                    'code' => 0,
                    'msg' => '保存成功',
                    'data' => [
                        'report_id' => $reportId,
                        'school_id' => $schoolId,
                        'field_name' => $fieldName,
                        'affected_rows' => $result
                    ]
                ]);
            } else {
                throw new \Exception('更新数据失败');
            }

        } catch (\Exception $e) {
            Db::rollback();
            Log::error('保存编辑字段异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '保存失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 字段重新生成（流式接口）
     *
     * @param Request $request
     * @return \support\Response
     */
    public function regenerateField(Request $request)
    {
        try {
            $params = $request->all();

            // 验证必需参数
            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            if (!isset($params['school_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校ID不能为空',
                    'data' => null
                ]);
            }

            if (empty($params['field_name'])) {
                return json([
                    'code' => 1,
                    'msg' => '字段名不能为空',
                    'data' => null
                ]);
            }

            $reportId = $params['report_id'];
            $schoolId = $params['school_id'];
            $fieldName = $params['field_name'];

            Log::info('开始重新生成字段', [
                'report_id' => $reportId,
                'school_id' => $schoolId,
                'field_name' => $fieldName
            ]);

            // 设置响应头，启用SSE
            $response = response('');
            $response->withHeaders([
                'Content-Type' => 'text/event-stream',
                'Cache-Control' => 'no-cache',
                'Connection' => 'keep-alive',
                'X-Accel-Buffering' => 'no' // 禁用Nginx缓冲
            ]);

            $connection = $request->connection;
            $id = Timer::add(1, function () use ($connection, &$id, $reportId, $schoolId, $fieldName) {
                $connection->send(new ServerSentEvents(['data' => "start"]));
                Log::info("开始重新生成字段");

                try {
                    if ($schoolId == '-1') {
                        // 高性价比院校推荐原因
                        $this->regenerateHighRecommendReasonStream($reportId, $connection);
                    } else {
                        Log::info("开始重新生成院校字段");
                        // 院校竞争难度和备考目标建议
                        $this->regenerateSchoolFieldStream($reportId, $schoolId, $fieldName, $connection);
                    }
                } catch (\Exception $e) {
                    Log::error('重新生成字段异常: ' . $e->getMessage());
                    $connection->send(new ServerSentEvents([
                        'event' => 'error',
                        'data' => json_encode(['error' => $e->getMessage()])
                    ]));
                }
            }, [], false);

            return $response;

        } catch (\Exception $e) {
            Log::error('重新生成字段初始化异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '初始化失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 重新生成院校字段（流式版本）
     *
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     * @param TcpConnection $connection
     */
    private function regenerateSchoolFieldStream($reportId, $schoolId, $fieldName, $connection)
    {
        try {
            Log::info('开始重新生成院校字段', [
                'report_id' => $reportId,
                'school_id' => $schoolId,
                'field_name' => $fieldName
            ]);
            // 1. 获取报告基本信息
            $schoolInfo = ReportInfo::where('report_id', $reportId)
                ->where('school_id', $schoolId)
                ->find();

            if (!$schoolInfo) {
                throw new \Exception('未找到报告信息');
            }
            // 2. 获取院校信息
            $reportInfo = SchoolReport::where('id', $reportId)->with(['student'])
                ->where('id', $reportId)
                ->find();

            if (!$schoolInfo) {
                throw new \Exception('未找到院校信息');
            }

            // 3. 获取院校详细信息
            $schoolDetail = SchoolInfo::where('id', $schoolId)->find();
            if (!$schoolDetail) {
                throw new \Exception('未找到院校详细信息');
            }
            //Log::info('schoolDetail: ' . json_encode($schoolDetail));

            // 4. 获取录取数据
            $admissionData = $this->getAdmissionDataForRegenerate($schoolId, $schoolDetail['major_code']);

          

            // 5. 构建AI提示词
            $prompt = $this->buildRegeneratePrompt($reportInfo,$schoolDetail,$schoolInfo, $admissionData, $fieldName);
            Log::info('重新生成院校字段提示词: ' . $prompt);
            // 6. 调用AI接口（流式）
            $this->callAIForRegenerateStream($prompt, $reportId, $schoolId, $fieldName, $connection);

        } catch (\Exception $e) {
            Log::error('重新生成院校字段失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 重新生成院校字段（竞争难度和备考目标建议）
     *
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     */
    private function regenerateSchoolField($reportId, $schoolId, $fieldName)
    {
        try {
            // 1. 获取报告基本信息
            $reportInfo = Db::name('ba_report_info')
                ->where('report_id', $reportId)
                ->where('school_id', $schoolId)
                ->find();

            if (!$reportInfo) {
                throw new \Exception('未找到报告信息');
            }

            // 2. 获取院校信息
            $schoolInfo = Db::name('ba_school_report')
                ->where('report_id', $reportId)
                ->where('school_id', $schoolId)
                ->find();

            if (!$schoolInfo) {
                throw new \Exception('未找到院校信息');
            }

            // 3. 获取院校详细信息
            $schoolDetail = Db::name('ba_school_info')
                ->where('school_id', $schoolId)
                ->find();

            // 4. 获取录取数据
            $admissionData = $this->getAdmissionDataForRegenerate($schoolId, $reportInfo['target_major_code']);

            // 5. 构建AI提示词
            $prompt = $this->buildRegeneratePrompt($reportInfo, $schoolInfo, $schoolDetail, $admissionData, $fieldName);

            // 6. 调用AI接口
            $this->callAIForRegenerate($prompt, $reportId, $schoolId, $fieldName);

        } catch (\Exception $e) {
            Log::error('重新生成院校字段失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 重新生成高性价比院校推荐原因（流式版本）
     *
     * @param int $reportId
     * @param TcpConnection $connection
     */
    private function regenerateHighRecommendReasonStream($reportId, $connection)
    {
        try {
            // 1. 获取报告基本信息
            $reportInfo = SchoolReport::where('id', $reportId)->find();

            if (!$reportInfo) {
                throw new \Exception('未找到报告信息');
            }

            // 2. 获取所有推荐院校
            $allSchools = ReportInfo::where('report_id', $reportId)
                ->field('school_id, school_name')
                ->select();

            // 3. 获取高性价比推荐院校
            $highRecommendSchools = ReportInfo::where('report_id', $reportId)
                ->where('is_high_recommend', 1)
                ->field('school_id, school_name')
                ->select();

            // 4. 构建AI提示词
            $prompt = $this->buildHighRecommendPrompt($reportInfo, $allSchools, $highRecommendSchools);

            // 5. 调用AI接口（流式）
            $this->callAIForHighRecommendStream($prompt, $reportId, $connection);

        } catch (\Exception $e) {
            Log::error('重新生成高性价比推荐原因失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 重新生成高性价比院校推荐原因
     *
     * @param int $reportId
     */
    private function regenerateHighRecommendReason($reportId)
    {
        try {
            // 1. 获取报告基本信息
            $reportInfo = Db::name('ba_report_info')
                ->where('report_id', $reportId)
                ->find();

            if (!$reportInfo) {
                throw new \Exception('未找到报告信息');
            }

            // 2. 获取所有推荐院校
            $allSchools = Db::name('ba_school_report')
                ->where('report_id', $reportId)
                ->field('school_id, school_name')
                ->select();

            // 3. 获取高性价比推荐院校
            $highRecommendSchools = Db::name('ba_report_info')
                ->where('report_id', $reportId)
                ->where('is_high_recommend', 1)
                ->field('school_id, school_name')
                ->select();

            // 4. 构建AI提示词
            $prompt = $this->buildHighRecommendPrompt($reportInfo, $allSchools, $highRecommendSchools);

            // 5. 调用AI接口
            $this->callAIForHighRecommend($prompt, $reportId);

        } catch (\Exception $e) {
            Log::error('重新生成高性价比推荐原因失败: ' . $e->getMessage());
            throw $e;
        }
    }



    /**
     * 流式调用阿里云千问API
     *
     * @param string $content 提示词
     * @param callable $callback 回调函数，用于处理每个数据块
     * @return void
     * @throws \Exception
     */
    private function streamQianwenApi($content, $callback)
    {
        $prompt = $content;
        try {
            // 获取配置信息
            $appId = config('qianwen.recommendApi')['appid'];
            $apiKey = config('qianwen.recommendApi')['key'];
            
            if (empty($appId) || empty($apiKey)) {
                throw new \Exception('千问API配置信息不完整');
            }
            
            $url = "https://dashscope.aliyuncs.com/api/v1/apps/$appId/completion";
            
            // 构建请求数据
            $data = [
                "input" => [
                    'prompt' => $prompt
                ],
                "parameters" => [
                    "incremental_output" => true
                ]
            ];
            
            // 将数据编码为 JSON
            $dataString = json_encode($data);
            
            // 检查 json_encode 是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("JSON encoding failed with error: " . json_last_error_msg());
            }
            
            Log::info('千问API请求 - URL: ' . $url);
            Log::info('千问API请求 - 数据: ' . $dataString);
            
            // 初始化 cURL 对话
            $ch = curl_init($url);
            
            // 设置 cURL 选项
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // 流式处理必须设为false
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 600);
            
            // 初始化缓冲区和状态变量
            $buffer = '';
            $schoolListProcessed = false;
            // 设置流式处理回调函数
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use ($callback, &$buffer, &$schoolListProcessed,$content) {
                
                // 处理SSE格式的数据
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) {
                        continue;
                    }
                    
                    // 处理SSE格式的data行
                    if (strpos($line, 'data:') === 0) {
                        $jsonData = substr($line, 5); // 去掉 "data:" 前缀
                        
                        try {
                            $decoded = json_decode($jsonData, true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                
                                // 提取text字段
                                if (isset($decoded['output']['text'])) {
                                    $text = $decoded['output']['text'];
                                    //直接输出 不再提取
                                    $callback($text);
                                    // // 检查是否遇到 % 分隔符且尚未处理院校列表
                                    // if (!$schoolListProcessed) {
                                    //     // 将新文本添加到缓冲区
                                    //     $buffer .= $text;
                                    
                                    //     if(strpos($buffer, '%') !== false){
                                    //         // 处理院校列表
                                    //         $this->processSchoolListAndCallSpider($buffer, $content[1]);
                                    //         $schoolListProcessed = true;
                                            
                                    //         // 找到 % 的位置，获取 % 之后的内容
                                    //         $percentPos = strpos($buffer, '%');
                                    //         $remainingContent = substr($buffer, $percentPos + 1);
                                            
                                    //         // 清空缓冲区，只保留 % 之后的内容
                                    //         $buffer = null;
                                            
                                    //         // 如果 % 之后有内容，立即发送
                                    //         if (!empty(trim($remainingContent))) {
                                    //             $callback($remainingContent);
                                    //         }
                                    //     }
                                    // } else if ($schoolListProcessed) {
                                    //     // 院校列表已处理，直接发送新内容
                                    //     $callback($text);
                                    // }
                                    // 如果院校列表尚未处理且没有遇到 %，继续积累到缓冲区中
                                }
                            } else {
                                Log::error('JSON解析失败: ' . json_last_error_msg());
                            }
                        } catch (\Exception $e) {
                            Log::error('解析千问响应异常: ' . $e->getMessage());
                        }
                    }
                }
                
                return strlen($data);
            });
            
            // 执行请求
            $response = curl_exec($ch);
            
            // 检查 cURL 执行是否成功
            if ($response === false) {
                $error = curl_error($ch);
                Log::error("千问API cURL错误: " . $error);
                curl_close($ch);
                throw new \Exception("cURL Error: " . $error);
            }
            
            // 获取 HTTP 状态码
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            Log::info("千问API HTTP状态码: " . $status_code);
            
            // 关闭 cURL 对话
            curl_close($ch);
            
            // 检查状态码
            if ($status_code != 200) {
                Log::error("千问API请求失败，状态码: $status_code");
                throw new \Exception("API请求失败，状态码: $status_code");
            }
            
            // 发送结束信号
            Log::info('千问API调用完成，发送结束信号');
            $callback('done');
            
        } catch (\Exception $e) {
            Log::error('千问流式API调用异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 处理院校列表并调用爬虫接口
     *
     * @param string $buffer 
     * @param string $major_code  包含院校列表的缓冲区内容
     * @return void
     */
    private function processSchoolListAndCallSpider($buffer, $major_code)
    {
        try {
            Log::info('开始处理院校列表，缓冲区内容: ' . $buffer);
            
            // 找到 % 的位置，提取院校列表部分
            $percentPos = strpos($buffer, '%');
            if ($percentPos === false) {
                Log::warning('未找到 % 分隔符');
                return;
            }
            
            $schoolListContent = substr($buffer, 0, $percentPos);
            Log::info('提取的院校列表内容: ' . $schoolListContent);
            
            // 解析院校名称（假设格式为：院校1、院校2、院校3）
            $schoolNames = $this->extractSchoolNamesFromContent($schoolListContent);
            
            if (empty($schoolNames)) {
                Log::warning('未能提取到院校名称');
                return;
            }
            
            Log::info('提取到的院校名称: ' . json_encode($schoolNames, JSON_UNESCAPED_UNICODE));
            
            // 查询院校ID
            $schoolIds = $this->getSchoolIdsByNames($schoolNames, $major_code);
            
            if (!empty($schoolIds)) {
                Log::info('查询到的院校ID: ' . json_encode($schoolIds));
                
                // 调用爬虫接口
                $this->callSpiderApi($schoolIds);
            } else {
                Log::warning('未查询到任何院校ID');
            }
            
        } catch (\Exception $e) {
            Log::error('处理院校列表异常: ' . $e->getMessage());
            // 不抛出异常，避免中断流式输出
        }
    }

    /**
     * 从内容中提取院校名称
     *
     * @param string $content 包含院校列表的内容
     * @return array 院校名称数组
     */
    private function extractSchoolNamesFromContent($content)
    {
        try {
            // 院校以','结尾，直接使用逗号分割
            $schoolNames = [];
            
            // 使用逗号分割院校列表
            $names = explode(',', $content);
            foreach ($names as $name) {
                $name = trim($name);
                if (!empty($name)) {
                    $schoolNames[] = $name;
                }
            }
            
            Log::info('提取到的院校名称: ' . json_encode($schoolNames, JSON_UNESCAPED_UNICODE));
            
            return array_unique($schoolNames);
            
        } catch (\Exception $e) {
            Log::error('提取院校名称异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据院校名称查询院校ID
     *
     * @param array $schoolNames 院校名称数组
     * @param string $marjor_code 专业代码
     * @return array 院校ID数组
     */
    private function getSchoolIdsByNames($schoolNames, $marjor_code)
    {
        try {
            if (empty($schoolNames)) {
                return [];
            }
            
            // 使用SchoolInfo模型查询院校ID
            return SchoolInfo::getIdsByNames($schoolNames, $marjor_code);
            
        } catch (\Exception $e) {
            Log::error('查询院校ID异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 调用爬虫API接口
     *
     * @param array $schoolIds 院校ID数组
     * @return bool
     */
    private function callSpiderApi($schoolIds):bool
    {
        try {
            if (empty($schoolIds)) {
                Log::warning('院校ID为空，跳过爬虫调用');
                return false;
            }
            // 后台爬取
            $spiderUrl = config('app.start_spider_school_info_url');
            $spiderUrlSync = config('app.start_spider_school_info_url_simple');
            if (empty($spiderUrl)) {
                Log::error('爬虫接口URL未配置');
                return false;
            }
            
            $requestData = [
                'ids' => $schoolIds
            ];
            
            Log::info('调用爬虫接口 - URL: ' . $spiderUrl);
            Log::info('调用爬虫接口 - 参数: ' . json_encode($requestData, JSON_UNESCAPED_UNICODE));
            
            // 使用异步方式调用，避免阻塞流式输出
            $headers = [
                'Content-Type' => 'application/json'
            ];
            
            // 使用已有的 http_post_json 函数调用爬虫接口
            $response = http_post_json($spiderUrl, $requestData, $headers, 300); // 300秒超时
            //添加异步爬取任务
            http_post_json($spiderUrlSync, $requestData, $headers, 300); // 300秒超时
            if ($response !== false) {
                Log::info('爬虫接口调用成功 - 响应: ' . $response);
                return true;
            } else {
                Log::error('爬虫接口调用失败');
                return false;
            }
            
        } catch (\Exception $e) {
            Log::error('调用爬虫接口异常: ' . $e->getMessage());
            // 不抛出异常，避免中断流式输出
            return false;
        }
    }

    public function testGetSchoolIds()
    {
        $schoolNames = ['安徽大学','中国科学技术大学','合肥工业大学','南京大学','东南大学','南京航空航天大学','浙江大学','杭州电子科技大学','苏州大学','江南大学'];
        $schoolIds = SchoolInfo::getIdsByNames($schoolNames, '081200');
        return json(['code' => 0, 'msg' => 'success', 'data' => $schoolIds]);
    }

    /**
     * 保存目标分数
     */
    public function saveTargetScores()
    {
        try {
            $data = request()->all();

            // 验证必要参数
            if (empty($data['report_id'])) {
                return json(['code' => 1, 'msg' => '缺少报告ID', 'data' => null]);
            }

            $reportId = $data['report_id'];
            $politics = intval($data['politics'] ?? 0);
            $english = intval($data['english'] ?? 0);
            $business1 = intval($data['business1'] ?? 0);
            $business2 = intval($data['business2'] ?? 0);
            $total = intval($data['total'] ?? 0);

            // 检查是否已存在记录
            $existingRecord = Db::name('target_scores')
                ->where('report_id', $reportId)
                ->find();

            if ($existingRecord) {
                // 更新现有记录
                $result = Db::name('target_scores')
                    ->where('report_id', $reportId)
                    ->update([
                        'politics' => $politics,
                        'english' => $english,
                        'business1' => $business1,
                        'business2' => $business2,
                        'total' => $total,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } else {
                // 插入新记录
                $result = Db::name('target_scores')
                    ->insert([
                        'report_id' => $reportId,
                        'politics' => $politics,
                        'english' => $english,
                        'business1' => $business1,
                        'business2' => $business2,
                        'total' => $total,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            if ($result !== false) {
                Log::info("目标分数保存成功 - 报告ID: {$reportId}, 政治: {$politics}, 英语: {$english}, 业务课一: {$business1}, 业务课二: {$business2}, 总分: {$total}");
                return json(['code' => 0, 'msg' => '保存成功', 'data' => null]);
            } else {
                Log::error("目标分数保存失败 - 报告ID: {$reportId}");
                return json(['code' => 1, 'msg' => '保存失败', 'data' => null]);
            }

        } catch (\Exception $e) {
            Log::error('保存目标分数异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '保存失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 获取目标分数
     */
    public function getTargetScores()
    {
        try {
            $reportId = request()->get('report_id');
            if( !is_numeric($reportId)) {
                $detail = Db::name('school_report')->where('idcode',$reportId)->find();
                $reportId = $detail['id'];
            }
            if (empty($reportId)) {
                return json(['code' => 1, 'msg' => '缺少报告ID', 'data' => null]);
            }
            $targetScores = Db::name('target_scores')
                ->where('report_id', $reportId)
                ->find();

            if ($targetScores) {
                return json(['code' => 0, 'msg' => '获取成功', 'data' => $targetScores]);
            } else {
                // 返回默认值
                return json(['code' => 0, 'msg' => '暂无数据', 'data' => [
                    'politics' => 0,
                    'english' => 0,
                    'business1' => 0,
                    'business2' => 0,
                    'total' => 0
                ]]);
            }

        } catch (\Exception $e) {
            Log::error('获取目标分数异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    public function getStudyPlan()
    {
        $data = request()->all();
        $report_id = $data['report_id'];

        // 根据report_id获取报告信息
        $reportInfo = SchoolReport::alias('sr')
            ->join('ba_student s', 'sr.student_id = s.id')
            ->where('sr.id', $report_id)
            ->where('sr.is_delete', 0)
            ->field('s.name as student_name, s.undergraduate_major_name as undergraduate_major, s.undergraduate_school_name as undergraduate_school, sr.target_major, sr.weak_modules')
            ->find();

        if (!$reportInfo) {
            return json(['code' => 1, 'msg' => '报告信息不存在', 'data' => null]);
        }

        // 获取千问配置
        $config = config('qianwen.studyPlan');
        $baseUrl = config('qianwen.url');

        $appId = $config['appid'];
        $apiKey = $config['key'];

        // 构建API URL
        $url = $baseUrl . 'apps/' . $appId . '/completion';
        Log::info('请求地址'.$url);
        // 构建prompt（字符串格式）
        $prompt = "";
        // 检查薄弱模块是否为空
        if (!empty($reportInfo['weak_modules'])) {
            $prompt = "学生姓名：{$reportInfo['student_name']}，本科专业：{$reportInfo['undergraduate_major']}，本科院校：{$reportInfo['undergraduate_school']}，目标专业：{$reportInfo['target_major']}，薄弱模块：{$reportInfo['weak_modules']}。请为该学生制定详细的学习计划。";
        } else {
            $prompt = "学生姓名：{$reportInfo['student_name']}，本科专业：{$reportInfo['undergraduate_major']}，本科院校：{$reportInfo['undergraduate_school']}，目标专业：{$reportInfo['target_major']}。请为该学生制定详细的学习计划。";
        }

        // 准备请求数据
        $requestData = [
            'input' => [
                'prompt' => $prompt
            ],
        ];

        Log::info("学生信息: " . $prompt);
        try {
            // 使用 functions.php 中的 http_post_json 方法调用千问API
            $headers = [
                'Authorization' => 'Bearer ' . $apiKey
            ];
            $startTime = time();
            Log::info("请求开始时间: " . $startTime);
            $response = http_post_json($url, $requestData, $headers, 300);
            Log::info("请求结束时间: " . time());
            if ($response === false) {
                Log::error('千问API调用失败: 无响应');
                return json(['code' => 1, 'msg' => 'API调用失败', 'data' => null]);
            }
            $response = mb_convert_encoding($response, 'UTF-8', 'auto');

            $responseData = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('千问API响应JSON解析失败: ' . json_last_error_msg() . ', 响应: ' . $response);
                return json(['code' => 1, 'msg' => 'API响应格式错误', 'data' => null]);
            }

            if (isset($responseData['output']['text'])) {
                $aiResponse = $responseData['output']['text'];
                
                // 解析AI返回的JSON数据
                $parsedData = $this->parseStudyPlanResponse($aiResponse);

                if ($parsedData === null) {
                    Log::error('学习计划数据解析失败: ' . $aiResponse);
                    return json(['code' => 1, 'msg' => '学习计划数据解析失败', 'data' => null]);
                }
                Log::info('学习计划数据解析成功: ' . json_encode($parsedData, JSON_UNESCAPED_UNICODE));

                // 将解析后的数据写入数据库
                $saveResult = $this->saveStudyPlanToDatabase($report_id, $parsedData);
                if (!$saveResult) {
                    Log::error('学习计划数据保存到数据库失败');
                    return json(['code' => 1, 'msg' => '学习计划数据保存失败', 'data' => null]);
                }

                $endTime = time();
                Log::info('学习计划生成并保存成功，耗时：' . ($endTime - $startTime) . '秒');

                // 只返回保存成功的状态，不返回具体数据
                return json(['code' => 0, 'msg' => '学习计划生成并保存成功', 'data' => ['saved' => true]]);
            } else {
                Log::error('千问API响应格式异常: ' . $response);
                return json(['code' => 1, 'msg' => 'API响应格式异常', 'data' => null]);
            }

        } catch (\Exception $e) {
            Log::error('获取学习计划异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取学习计划失败: ' . $e->getMessage(), 'data' => null]);
        }
    }


    /**
     * 解析学习计划响应数据
     *
     * @param string $aiResponse AI返回的原始响应文本
     * @return array|null 解析后的学习计划数据，解析失败返回null
     */
    private function parseStudyPlanResponse($aiResponse)
    {
        try {
            Log::info('原始AI响应长度: ' . strlen($aiResponse));
            Log::info('原始AI响应前500字符: ' . substr($aiResponse, 0, 500));

            // 1. 清理响应文本，移除markdown代码块标记
            $cleanedResponse = $this->cleanJsonResponse($aiResponse);

            // 2. 尝试解析JSON数据
            $studyPlanData = json_decode($cleanedResponse, true);

            // 3. 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::info('JSON无效，开始清理。错误: ' . json_last_error_msg());

                // 尝试修复常见的JSON问题
                $fixedResponse = $this->fixJsonIssues($cleanedResponse);
                $studyPlanData = json_decode($fixedResponse, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON清理失败: ' . json_last_error_msg());
                    Log::info('清理后响应长度: ' . strlen($fixedResponse));
                    Log::info('清理后响应前500字符: ' . substr($fixedResponse, 0, 500));
                    Log::error('JSON解析失败: ' . json_last_error_msg());
                    Log::error('JSON错误代码: ' . json_last_error());
                    Log::error('导致错误的JSON片段: ' . substr($fixedResponse, 0, 1000));
                    return null;
                }
            }

            // 4. 验证数据结构
            if (!$this->validateStudyPlanStructure($studyPlanData)) {
                Log::error('学习计划数据结构验证失败');
                return null;
            }

            return $studyPlanData;

        } catch (\Exception $e) {
            Log::error('解析学习计划响应异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * 清理JSON响应文本
     *
     * @param string $response 原始响应文本
     * @return string 清理后的JSON字符串
     */
    private function cleanJsonResponse($response)
    {
        // 移除markdown代码块标记
        $cleaned = preg_replace('/```json\s*/', '', $response);
        $cleaned = preg_replace('/```\s*$/', '', $cleaned);

        // 移除首尾空白字符
        $cleaned = trim($cleaned);

        Log::info('清理后的JSON长度: ' . strlen($cleaned));

        return $cleaned;
    }

    /**
     * 修复常见的JSON问题
     *
     * @param string $jsonString 有问题的JSON字符串
     * @return string 修复后的JSON字符串
     */
    private function fixJsonIssues($jsonString)
    {
        try {
            // 1. 移除BOM标记
            $fixed = str_replace("\xEF\xBB\xBF", '', $jsonString);

            // 2. 统一换行符
            $fixed = str_replace(["\r\n", "\r"], "\n", $fixed);

            // 3. 移除控制字符（除了\n, \t）
            $fixed = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $fixed);

            // 4. 修复可能的编码问题
            if (!mb_check_encoding($fixed, 'UTF-8')) {
                $fixed = mb_convert_encoding($fixed, 'UTF-8', 'auto');
            }

            // 5. 尝试找到完整的JSON结构
            $fixed = $this->extractCompleteJson($fixed);

            // 6. 修复常见的JSON语法错误
            $fixed = $this->fixJsonSyntax($fixed);

            return $fixed;

        } catch (\Exception $e) {
            Log::error('修复JSON时发生异常: ' . $e->getMessage());
            return $jsonString; // 返回原始字符串
        }
    }

    /**
     * 提取完整的JSON结构
     *
     * @param string $text 包含JSON的文本
     * @return string 提取的JSON字符串
     */
    private function extractCompleteJson($text)
    {
        // 找到第一个 { 和最后一个 }
        $firstBrace = strpos($text, '{');
        $lastBrace = strrpos($text, '}');

        if ($firstBrace !== false && $lastBrace !== false && $lastBrace > $firstBrace) {
            return substr($text, $firstBrace, $lastBrace - $firstBrace + 1);
        }

        return $text;
    }

    /**
     * 修复JSON语法错误
     *
     * @param string $jsonString JSON字符串
     * @return string 修复后的JSON字符串
     */
    private function fixJsonSyntax($jsonString)
    {
        // 1. 修复截断的Unicode字符
        $fixed = $this->fixTruncatedUnicode($jsonString);

        // 2. 修复多余的逗号
        $fixed = preg_replace('/,(\s*[}\]])/', '$1', $fixed);

        // 3. 修复缺少的引号（简单情况）
        $fixed = preg_replace('/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/', '$1"$2":', $fixed);

        // 4. 修复字符串中的未转义引号
        $fixed = preg_replace('/(?<!\\\\)"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}\]])/', '"\1\"\2\"\3"', $fixed);

        // 5. 确保JSON结构完整
        $fixed = $this->ensureCompleteJsonStructure($fixed);

        return $fixed;
    }

    /**
     * 修复截断的Unicode字符
     *
     * @param string $text 可能包含截断Unicode字符的文本
     * @return string 修复后的文本
     */
    private function fixTruncatedUnicode($text)
    {
        // 移除末尾的不完整Unicode字符
        $fixed = preg_replace('/[\x80-\xFF]+$/', '', $text);

        // 如果字符串以不完整的中文字符结尾，尝试修复
        if (preg_match('/[^\x00-\x7F]+$/', $text)) {
            // 找到最后一个完整的中文字符位置
            $lastValidPos = 0;
            for ($i = strlen($text) - 1; $i >= 0; $i--) {
                $char = substr($text, $i, 1);
                if (ord($char) < 128) { // ASCII字符
                    $lastValidPos = $i + 1;
                    break;
                } elseif (mb_check_encoding(substr($text, $i), 'UTF-8')) {
                    $lastValidPos = $i;
                    break;
                }
            }

            if ($lastValidPos > 0) {
                $fixed = substr($text, 0, $lastValidPos);
            }
        }

        return $fixed;
    }

    /**
     * 确保JSON结构完整
     *
     * @param string $jsonString JSON字符串
     * @return string 完整的JSON字符串
     */
    private function ensureCompleteJsonStructure($jsonString)
    {
        $openBraces = substr_count($jsonString, '{');
        $closeBraces = substr_count($jsonString, '}');
        $openBrackets = substr_count($jsonString, '[');
        $closeBrackets = substr_count($jsonString, ']');

        // 补充缺失的闭合括号
        $missingCloseBraces = $openBraces - $closeBraces;
        $missingCloseBrackets = $openBrackets - $closeBrackets;

        $fixed = $jsonString;

        // 添加缺失的闭合方括号
        for ($i = 0; $i < $missingCloseBrackets; $i++) {
            $fixed .= ']';
        }

        // 添加缺失的闭合大括号
        for ($i = 0; $i < $missingCloseBraces; $i++) {
            $fixed .= '}';
        }

        return $fixed;
    }

    /**
     * 验证学习计划数据结构
     *
     * @param array $data 解析后的数据
     * @return bool 验证是否通过
     */
    private function validateStudyPlanStructure($data)
    {
        // 检查必需的顶级字段
        if (!isset($data['weakModuleAnalysis']) || !isset($data['studyPlanning']) || !isset($data['comprehensiveAdvice'])) {
            Log::error('缺少必需的顶级字段');
            return false;
        }

        // 检查weakModuleAnalysis是否为数组
        if (!is_array($data['weakModuleAnalysis'])) {
            Log::error('weakModuleAnalysis不是数组格式');
            return false;
        }

        // 检查studyPlanning结构
        $studyPlanning = $data['studyPlanning'];
        if (!isset($studyPlanning['title']) || !isset($studyPlanning['stages']) || !is_array($studyPlanning['stages'])) {
            Log::error('studyPlanning结构不正确');
            return false;
        }

        // 检查stages中的每个阶段
        foreach ($studyPlanning['stages'] as $stage) {
            if (!isset($stage['id']) || !isset($stage['title']) || !isset($stage['modules']) || !is_array($stage['modules'])) {
                Log::error('stage结构不正确');
                return false;
            }

            // 检查modules中的每个模块
            foreach ($stage['modules'] as $module) {
                $requiredFields = ['id', 'name', 'studyContent', 'studyMethod', 'studyMaterials', 'studyReminder'];
                foreach ($requiredFields as $field) {
                    if (!isset($module[$field])) {
                        Log::error("module缺少必需字段: {$field}");
                        return false;
                    }
                }
            }
        }

        Log::info('学习计划数据结构验证通过');
        return true;
    }


    /**
     * 递归转换换行符（已废弃，保留以防兼容性问题）
     *
     * @param mixed $value 要处理的值
     * @return mixed 处理后的值
     */
    private function convertNewlinesRecursive($value)
    {
        // 不再进行换行符转换，直接返回原值
        return $value;
    }

    public function textparseStudyPlanResponse()
    {
        // 使用您提供的完整响应数据进行测试
        $data = '{"output":{"finish_reason":"stop","reject_status":false,"session_id":"b13aea61a6ad4fa99dc6eb274accbabb","text":"```json\n{\n \"weakModuleAnalysis\": [],\n \"studyPlanning\": {\n \"title\": \"初试各科学习规划\",\n \"stages\": [\n {\n \"id\": \"stage1\",\n \"title\": \"第一阶段：夯实基础（2025年6月18日-8月31日）\",\n \"modules\": [\n {\n \"id\": \"politics\",\n \"name\": \"政治模块\",\n \"studyContent\": \"1. 系统学习马克思主义基本原理概论：唯物论、辩证法、认识论、唯物史观\\n2. 毛泽东思想和中国特色社会主义理论体系概论：新民主主义革命理论、社会主义改造理论、改革开放理论\\n3. 中国近现代史纲要：1840-1949年重大历史事件、1949-1978年社会主义建设探索\\n4. 思想道德修养与法律基础：社会主义核心价值观、法治体系、道德规范\",\n \"studyMethod\": \"1. 每天安排1.5小时政治学习时间，建议上午9:00-10:30\\n2. 观看徐涛/腿姐基础班视频课，同步做《肖秀荣1000题》对应章节\\n3. 建立思维导图笔记体系，每章结束后用XMind整理知识框架\\n4. 每周日复盘本周所学内容，做对应章节的历年真题选择题\",\n \"studyMaterials\": \"核心资料：\\n1. 《肖秀荣精讲精练》：知识点全面，配套1000题使用效果佳\\n2. 《徐涛核心考案》：逻辑清晰，适合基础阶段构建框架\\n使用方法：先看视频课理解，再阅读教材标注重点，最后做题巩固\",\n \"studyReminder\": \"1. 不要过早背诵，重在理解基本概念和理论逻辑\\n2. 避免只看视频不做题，每学习完一章必须完成对应练习\\n3. 注意区分相似概念（如主要矛盾/基本矛盾）\"\n },\n {\n \"id\": \"english\",\n \"name\": \"英语模块\",\n \"studyContent\": \"1. 考研大纲词汇5500词系统突破（重点掌握2000核心词）\\n2. 基础语法重构：三大从句、非谓语动词、虚拟语气等\\n3. 长难句分析训练：每日精析3-5句真题长难句\\n4. 基础阅读能力培养：精读1998-2005年真题文章\",\n \"studyMethod\": \"1. 使用艾宾浩斯记忆法背单词：每日新学80词+复习前5日内容\\n2. 语法学习配合《句句真研》做专项练习，每天30分钟\\n3. 长难句分析步骤：划分结构→翻译→对照解析→整理生词\\n4. 精读训练：每篇文章完成3遍阅读（速读→精读→复盘）\",\n \"studyMaterials\": \"核心资料：\\n1. 《红宝书考研英语词汇》或《恋练有词》\\n2. 《句句真研》语法书（田静）\\n3. 《考研真相》基础版（1998-2005年真题）\\n推荐理由：词汇书收词全面，语法书讲解细致通俗，真题解析适合基础阶段\",\n \"studyReminder\": \"1. 单词记忆要结合例句，避免孤立背词\\n2. 长难句分析需坚持每天练习，建立句子敏感度\\n3. 精读时不求速度，务必弄懂每个句子结构和生词\"\n },\n {\n \"id\": \"math\",\n \"name\": \"数学模块\",\n \"studyContent\": \"1. 高等数学：函数极限、导数与微分、中值定理、不定积分、定积分应用、多元微分、二重积分\\n2. 线性代数：行列式计算、矩阵运算、向量组线性相关性、方程组求解\\n3. 概率统计：随机事件概率、随机变量分布、数字特征、大数定律\",\n \"studyMethod\": \"1. 每天3小时数学时间，保持上午头脑清醒时段学习\\n2. 配合张宇/武忠祥基础课视频，做《复习全书·基础篇》例题\\n3. 整理错题本：记录典型错题+错误原因+正确解法\\n4. 每周做章节测试（《660题》基础部分）检测学习效果\",\n \"studyMaterials\": \"核心资料：\\n1. 《李永乐复习全书·基础篇》\\n2. 《张宇考研数学基础30讲》\\n3. 《汤家凤1800题》基础部分\\n特点：全书知识体系完整，30讲配套视频讲解细致，1800题题量充足\",\n \"studyReminder\": \"1. 重视计算能力培养，避免眼高手低\\n2. 定理证明要理解推导过程而非死记硬背\\n3. 线性代数注意知识串联，建立矩阵-向量-方程组关联\"\n }\n ]\n },\n {\n \"id\": \"stage2\",\n \"title\": \"第二阶段：强化提高（2025年9月1日-10月31日）\",\n \"modules\": [\n {\n \"id\": \"politics\",\n \"name\": \"政治模块\",\n \"studyContent\": \"1. 各学科重点难点突破：马原政经部分、史纲时间轴梳理、毛中特新时代理论\\n2. 题型专项训练：单选题排错技巧、多选题全选判断、分析题答题框架\\n3. 时政热点积累：每月重大时政事件整理（1-8月时政）\",\n \"studyMethod\": \"1. 使用《腿姐冲刺背诵手册》建立关键词记忆体系\\n2. 二刷《1000题》错题，整理高频错误知识点\\n3. 参加徐涛/腿姐强化班，学习分析题答题模板\\n4. 制作易混知识点对比表格（如会议、土地政策）\",\n \"studyMaterials\": \"核心资料：\\n1. 《腿姐冲刺背诵手册》\\n2. 《徐涛优题库》\\n3. 《肖秀荣知识点提要》\\n强化阶段需配合习题集和背诵手册，构建答题思维\",\n \"studyReminder\": \"1. 开始关注分析题答题方法，但不必过早背诵\\n2. 时政学习要结合考点，避免泛泛了解\\n3. 多选题为提分关键，需重点突破\"\n },\n {\n \"id\": \"english\",\n \"name\": \"英语模块\",\n \"studyContent\": \"1. 真题精做（2006-2018年）：完形填空、阅读理解、新题型专项突破\\n2. 翻译技巧提升：英译汉拆解重组训练\\n3. 写作基础构建：小作文类型识别、大作文框架搭建\\n4. 核心词汇深化：真题高频词、熟词僻义专项整理\",\n \"studyMethod\": \"1. 每周精做3套真题，限时完成（不含作文）\\n2. 阅读训练采用\"题型归类法\"，总结细节题/主旨题解题技巧\\n3. 翻译每日练习2句，重点训练定语从句、被动语态处理\\n4. 积累写作语料库：分类整理环境保护、教育等高频话题\",\n \"studyMaterials\": \"核心资料：\\n1. 《张剑黄皮书》（2006-2018年）\\n2. 《王江涛考研英语高分写作》\\n3. 《唐静拆分与组合翻译法》\\n真题解析权威，写作模板实用，翻译方法系统\",\n \"studyReminder\": \"1. 真题要反复研究，至少做2-3遍\\n2. 阅读错误选项要分析命题人设置规律\\n3. 写作避免死背模板，要灵活组合素材\"\n },\n {\n \"id\": \"math\",\n \"name\": \"数学模块\",\n \"studyContent\": \"1. 高数强化：微分方程、级数判别、曲线曲面积分、物理应用\\n2. 线代强化：特征值、二次型、相似对角化\\n3. 概率强化：统计量分布、参数估计、假设检验\\n4. 综合题型训练：证明题、应用题解题思路\",\n \"studyMethod\": \"1. 跟听张宇/武忠祥强化班，做《李永乐强化330题》\\n2. 建立题型分类本：如中值定理7大证明方法\\n3. 每周模拟1套真题（2010-2015年），严格计时\\n4. 参加数学答疑群，及时解决疑难问题\",\n \"studyMaterials\": \"核心资料：\\n1. 《李永乐数学历年真题全精解析》\\n2. 《张宇高等数学18讲》\\n3. 《李林考前冲刺880题》\\n真题解析详细，专题讲解深入，模拟题质量高\",\n \"studyReminder\": \"1. 注重解题思路总结，避免题海战术\\n2. 真题要按套卷做，培养整体时间把控\\n3. 考试策略训练：学会合理放弃难题\"\n }\n ]\n },\n {\n \"id\": \"stage3\",\n \"title\": \"第三阶段：冲刺模考（2025年11月1日-12月20日）\",\n \"modules\": [\n {\n \"id\": \"politics\",\n \"name\": \"政治模块\",\n \"studyContent\": \"1. 时政热点系统梳理（全年重大事件+考点预测）\\n2. 分析题专题突破：马原原理匹配、当代大题押题\\n3. 全真模拟训练：近3年真题+8套模拟卷选择题\\n4. 高频考点终极背诵：会议/著作/土地政策汇总\",\n \"studyMethod\": \"1. 背诵《肖四》分析题答案，掌握答题话术\\n2. 参加腿姐押题班，整理万能答题模板\\n3. 每天1套模拟卷（肖八/徐六/腿四），限时35分钟\\n4. 制作背诵卡片：随身携带记忆核心表述\",\n \"studyMaterials\": \"核心资料：\\n1. 《肖秀荣冲刺8套卷》+《终极预测4套卷》\\n2. 《腿姐冲刺预测4套卷》\\n3. 《徐涛预测20题》\\n押题卷必做，分析题答案要反复背诵\",\n \"studyReminder\": \"1. 分析题背诵要抓关键句，不必逐字记忆\\n2. 时政学习要结合各学科交叉考点\\n3. 保持选择题手感至考前最后一天\"\n },\n {\n \"id\": \"english\",\n \"name\": \"英语模块\",\n \"studyContent\": \"1. 2019-2025年真题全真模拟（含作文）\\n2. 作文模板定制：个性化整理3套万能模板\\n3. 高频错题复盘：重做5年内错题\\n4. 考前必背：核心词汇+写作高分句型\",\n \"studyMethod\": \"1. 每周六14:00-17:00全真模拟考试\\n2. 作文批改服务：找专业人士修改3篇以上\\n3. 整理自己的\"错题档案\"，分析错误规律\\n4. 晨读背诵作文范文，培养语感\",\n \"studyMaterials\": \"核心资料：\\n1. 《近5年真题套卷》\\n2. 《王江涛考前预测20篇》\\n3. 《考研英语高分写作字帖》\\n预测作文参考价值高，字迹工整可加分\",\n \"studyReminder\": \"1. 严格控制做题时间（阅读每篇15-18分钟）\\n2. 写作要自己动手写，避免只背不练\\n3. 保持每日英语语感至考前一天\"\n },\n {\n \"id\": \"math\",\n \"name\": \"数学模块\",\n \"studyContent\": \"1. 近10年真题分类解析（2015-2025年）\\n2. 押题卷限时训练：李林6+4、合工大超越卷\\n3. 查漏补缺：根据错题回归基础概念\\n4. 考场策略演练：时间分配、草稿使用规范\",\n \"studyMethod\": \"1. 每天上午按考试时间做1套押题卷\\n2. 建立\"最后50天错题本\"，只记录核心错误\\n3. 参加模考班，体验真实考场氛围\\n4. 复习公式手册，确保不因记忆失分\",\n \"studyMaterials\": \"核心资料：\\n1. 《李林考前冲刺6套卷》+《终极预测4套卷》\\n2. 《合工大超越共创卷》\\n3. 《张宇考研数学命题人终极预测8套卷》\\n押题卷贴近真题难度，必做\",\n \"studyReminder\": \"1. 最后阶段仍要保持计算手感\\n2. 简单题确保100%正确率\\n3. 考试带齐工具（准考证、计算器等）\"\n }\n ]\n }\n ]\n },\n \"comprehensiveAdvice\": \"1. 时间管理：采用\"番茄工作法\"，每学习50分钟休息10分钟，每天保证8小时有效学习时间。每周日做学习复盘，调整下周计划\\n\\n2. 信息收集：定期查看研招网和目标院校计算机学院官网，重点关注：\\n - 9月发布的招生简章\\n - 10月网上报名注意事项\\n - 11月现场确认要求\\n - 12月考场安排\\n\\n3. 专业课备考：计算机统考408包括数据结构、计算机组成原理、操作系统、计算机网络四门课。建议：\\n - 使用《王道考研复习指导》系列教材\\n - 在Github搜索目标院校历年真题\\n - 参加计算机考研交流群获取最新资讯\\n\\n4. 健康管理：\\n - 保证每天7小时睡眠，避免熬夜\\n - 每周3次有氧运动（跑步/游泳等）\\n - 饮食均衡，适当补充Omega-3和维生素B族\\n - 每天做10分钟正念冥想缓解压力\\n\\n5. 心态调整：\\n - 建立成长型思维，把每次错题当作进步机会\\n - 与积极向上的研友组成学习小组\\n - 准备\"激励便签\"贴在书桌，写下考研初心\\n\\n计算机专业考研竞争激烈，但安师大计算机专业基础扎实，你有足够的实力实现目标。记住：考研是场马拉松，保持稳定节奏比短期冲刺更重要，坚持到最后就是胜利！\"\n}\n```"},"usage":{"models":[{"output_tokens":3169,"model_id":"deepseek-v3","input_tokens":3171}]},"request_id":"f16418d2-f826-93db-9e55-2f4d48e56ab8"}';
        $responseData = json_decode($data, true);

        $aiResponse = $responseData['output']['text'];
        Log::info('测试解析 - 原始AI响应长度: ' . strlen($aiResponse));
        $res = $this->parseStudyPlanResponse($aiResponse);

        if ($res === null) {
            return json(['code' => 1, 'msg' => '解析失败', 'data' => null]);
        }

        return json(['code' => 0, 'msg' => '解析成功', 'data' => $res]);
    }

    /**
     * 将学习计划数据保存到数据库
     *
     * @param int $reportId 报告ID
     * @param array $studyPlanData 学习计划数据
     * @return bool 保存是否成功
     */
    private function saveStudyPlanToDatabase($reportId, $studyPlanData)
    {
        try {
            // 开启事务
            Db::startTrans();

            // 1. 删除该报告ID的旧数据
            $this->deleteOldStudyPlanData($reportId);

            // 2. 保存薄弱模块分析数据
            if (isset($studyPlanData['weakModuleAnalysis']) && is_array($studyPlanData['weakModuleAnalysis'])) {
                $weakModuleResult = WeakModuleAnalysis::insertBatch($reportId, $studyPlanData['weakModuleAnalysis']);
                if (!$weakModuleResult) {
                    throw new \Exception('薄弱模块分析数据保存失败');
                }
                Log::info('薄弱模块分析数据保存成功，数量：' . count($studyPlanData['weakModuleAnalysis']));
            }

            // 3. 保存学习阶段数据并获取ID映射
            $stageIdMap = [];
            if (isset($studyPlanData['studyPlanning']['stages']) && is_array($studyPlanData['studyPlanning']['stages'])) {
                $stageIdMap = StudyStages::insertBatch($reportId, $studyPlanData['studyPlanning']['stages']);
                if (empty($stageIdMap)) {
                    throw new \Exception('学习阶段数据保存失败');
                }
                Log::info('学习阶段数据保存成功，数量：' . count($stageIdMap));

                // 4. 保存学习模块数据
                $moduleResult = StudyModules::insertBatch($reportId, $studyPlanData['studyPlanning']['stages'], $stageIdMap);
                if (!$moduleResult) {
                    throw new \Exception('学习模块数据保存失败');
                }
                Log::info('学习模块数据保存成功');
            }

            // 5. 保存综合建议数据
            if (isset($studyPlanData['comprehensiveAdvice']) && !empty($studyPlanData['comprehensiveAdvice'])) {
                $adviceResult = ComprehensiveAdvice::insertAdvice($reportId, $studyPlanData['comprehensiveAdvice']);
                if (!$adviceResult) {
                    throw new \Exception('综合建议数据保存失败');
                }
                Log::info('综合建议数据保存成功');
            }

            // 提交事务
            Db::commit();
            Log::info('学习计划数据全部保存成功，报告ID：' . $reportId);
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::error('保存学习计划数据异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 删除旧的学习计划数据
     *
     * @param int $reportId 报告ID
     * @return void
     */
    private function deleteOldStudyPlanData($reportId)
    {
        try {
            // 删除顺序很重要，先删除子表，再删除父表
            StudyModules::deleteByReportId($reportId);
            StudyStages::deleteByReportId($reportId);
            WeakModuleAnalysis::deleteByReportId($reportId);
            ComprehensiveAdvice::deleteByReportId($reportId);

            Log::info('旧学习计划数据删除成功，报告ID：' . $reportId);
        } catch (\Exception $e) {
            Log::error('删除旧学习计划数据异常: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 从数据库获取学习计划数据
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getStudyPlanFromDatabase(Request $request)
    {
        $data = $request->all();

        $reportId = $data['report_id'] ?? 0;
        if( !is_numeric($reportId)) {
            $detail = Db::name('school_report')->where('idcode',$reportId)->find();
            $reportId = $detail['id'];
        }

        if (empty($reportId)) {
            return json(['code' => 1, 'msg' => '报告ID不能为空', 'data' => null]);
        }

        try {
            // 1. 获取薄弱模块分析数据
            $weakModuleAnalysis = WeakModuleAnalysis::getByReportId($reportId);

            // 2. 获取学习阶段数据
            $studyStages = StudyStages::getByReportId($reportId);
//            var_dump($studyStages);
            // 3. 获取学习模块数据
            $studyModules = StudyModules::getByReportId($reportId);

            // 4. 获取综合建议数据
            $comprehensiveAdvice = ComprehensiveAdvice::getByReportId($reportId);

            // 5. 组装数据结构
            $studyPlanData = $this->assembleStudyPlanData($weakModuleAnalysis, $studyStages, $studyModules, $comprehensiveAdvice);

            if (empty($studyPlanData)) {
                return json(['code' => 1, 'msg' => '未找到学习计划数据', 'data' => null]);
            }

            return json(['code' => 0, 'msg' => 'success', 'data' => $studyPlanData]);

        } catch (\Exception $e) {
            Log::error('获取学习计划数据异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '获取学习计划数据失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 组装学习计划数据结构
     *
     * @param array $weakModuleAnalysis 薄弱模块分析数据
     * @param array $studyStages 学习阶段数据
     * @param array $studyModules 学习模块数据
     * @param array|null $comprehensiveAdvice 综合建议数据
     * @return array
     */
    private function assembleStudyPlanData($weakModuleAnalysis, $studyStages, $studyModules, $comprehensiveAdvice)
    {
        // 1. 处理薄弱模块分析数据
        $weakModuleData = [];
        foreach ($weakModuleAnalysis as $module) {
            $weakModuleData[] = [
                'id' => $module['id'], // 添加ID字段用于编辑
                'subject' => $module['subject'],
                'problemAnalysis' => $module['problem_analysis'],
                'solutions' => $module['solutions']
            ];
        }

        // 2. 按阶段组织学习模块数据
        $modulesByStage = [];
        foreach ($studyModules as $module) {
            $stageId = $module['stage_id'];
            if (!isset($modulesByStage[$stageId])) {
                $modulesByStage[$stageId] = [];
            }
            $modulesByStage[$stageId][] = [
                'id' => $module['module_id'],
                'dbId' => $module['id'], // 添加数据库ID用于编辑
                'name' => $module['name'],
                'studyContent' => $module['study_content'],
                'studyMethod' => $module['study_method'],
                'studyMaterials' => $module['study_materials'],
                'studyReminder' => $module['study_reminder']
            ];
        }

        // 3. 组装学习规划数据
        $stages = [];
        foreach ($studyStages as $stage) {
            $stageId = $stage['id'];
            $stages[] = [
                'id' => 'stage' . $stage['sort_order'],
                'title' => $stage['title'],
                'modules' => $modulesByStage[$stageId] ?? []
            ];
        }

        $studyPlanning = [
            'title' => '初试各科学习规划',
            'stages' => $stages
        ];

        // 4. 组装完整数据结构
        $result = [
            'weakModuleAnalysis' => $weakModuleData,
            'studyPlanning' => $studyPlanning,
            'comprehensiveAdvice' => $comprehensiveAdvice ? $comprehensiveAdvice['advice_content'] : '',
            'comprehensiveAdviceId' => $comprehensiveAdvice ? $comprehensiveAdvice['id'] : null // 添加ID用于编辑
        ];

        return $result;
    }

    /**
     * 更新薄弱模块分析
     *
     * @param Request $request
     * @return \support\Response
     */
    public function updateWeakModuleAnalysis(Request $request)
    {
        $data = $request->all();

        // 验证必需参数
        $requiredFields = ['id', 'subject', 'problem_analysis', 'solutions'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return json(['code' => 1, 'msg' => "参数 {$field} 不能为空", 'data' => null]);
            }
        }

        try {
            $result = WeakModuleAnalysis::where('id', $data['id'])->update([
                'subject' => $data['subject'],
                'problem_analysis' => $data['problem_analysis'],
                'solutions' => $data['solutions'],
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                Log::info('薄弱模块分析更新成功，ID：' . $data['id']);
                return json(['code' => 0, 'msg' => '更新成功', 'data' => null]);
            } else {
                return json(['code' => 1, 'msg' => '更新失败，记录不存在', 'data' => null]);
            }

        } catch (\Exception $e) {
            Log::error('更新薄弱模块分析异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 更新学习模块
     *
     * @param Request $request
     * @return \support\Response
     */
    public function updateStudyModule(Request $request)
    {
        $data = $request->all();

        // 验证必需参数
        $requiredFields = ['id', 'name', 'study_content', 'study_method', 'study_materials', 'study_reminder'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return json(['code' => 1, 'msg' => "参数 {$field} 不能为空", 'data' => null]);
            }
        }

        try {
            $result = StudyModules::where('id', $data['id'])->update([
                'name' => $data['name'],
                'study_content' => $data['study_content'],
                'study_method' => $data['study_method'],
                'study_materials' => $data['study_materials'],
                'study_reminder' => $data['study_reminder'],
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                Log::info('学习模块更新成功，ID：' . $data['id']);
                return json(['code' => 0, 'msg' => '更新成功', 'data' => null]);
            } else {
                return json(['code' => 1, 'msg' => '更新失败，记录不存在', 'data' => null]);
            }

        } catch (\Exception $e) {
            Log::error('更新学习模块异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 更新综合建议
     *
     * @param Request $request
     * @return \support\Response
     */
    public function updateComprehensiveAdvice(Request $request)
    {
        $data = $request->all();

        // 验证必需参数
        if (!isset($data['id']) || !isset($data['advice_content'])) {
            return json(['code' => 1, 'msg' => '参数不完整', 'data' => null]);
        }

        try {
            $result = ComprehensiveAdvice::where('id', $data['id'])->update([
                'advice_content' => $data['advice_content'],
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                Log::info('综合建议更新成功，ID：' . $data['id']);
                return json(['code' => 0, 'msg' => '更新成功', 'data' => null]);
            } else {
                return json(['code' => 1, 'msg' => '更新失败，记录不存在', 'data' => null]);
            }

        } catch (\Exception $e) {
            Log::error('更新综合建议异常: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '更新失败: ' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 获取录取数据（用于重新生成）
     *
     * @param int $schoolId
     * @param string $majorCode
     * @return array
     */
    private function getAdmissionDataForRegenerate($schoolId, $majorCode)
    {
        try {
            // 获取录取分数线数据
            $admissionList = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('major_code', $majorCode)
                ->where('year', '=', date('Y')) // 最近3年数据
                ->field('initial_score, retest_score, total_score, year')
                ->order('year', 'desc')
                ->limit(20)
                ->select();

            // 获取复试数据
            $retestList = Db::name('retest_list')
                ->where('school_id', $schoolId)
                ->where('major_code', $majorCode)
                ->where('year', '=', date('Y'))
                ->field('initial_score, politics_score, english_score, major1_score, major2_score, year')
                ->order('year', 'desc')
                ->limit(20)
                ->select();

            return [
                'admission_list' => $admissionList,
                'retest_list' => $retestList
            ];

        } catch (\Exception $e) {
            Log::error('获取录取数据失败: ' . $e->getMessage());
            return [
                'admission_list' => [],
                'retest_list' => []
            ];
        }
    }

    /**
     * 构建重新生成提示词
     *
     * @param array $reportInfo
     * @param array $schoolInfo
     * @param array $schoolDetail
     * @param array $admissionData
     * @param string $fieldName
     * @return string
     */
    private function buildRegeneratePrompt($reportInfo, $schoolDetail,$schoolInfo, $admissionData, $fieldName)
    {
        $pos = mb_strpos($reportInfo['context'], "本科成绩");
        $prompt = substr($reportInfo['context'], 0, $pos); // 提取上下文的前半部分（不包含学校列表）
        if ($schoolDetail['school_type']) {
            $prompt .= "\n院校标签：{$schoolDetail['school_type']}\n";
        }

        //当前推荐的院校
        $prompt .= "当前推荐院校：{$schoolInfo['school_name']}\n";
        //预估总分
        if(!empty($reportInfo['total_score'])){
            $prompt .= "预估总分：{$reportInfo['total_score']}分\n";
        }
        // 添加录取数据
        if (!empty($admissionData['admission_list'])) {
            $prompt .= "\n历年录取数据：\n";
            foreach ($admissionData['admission_list'] as $admission) {
                $prompt .= "{$admission['year']}年：初试{$admission['initial_score']}分，复试{$admission['retest_score']}分，总分{$admission['total_score']}分\n";
            }
        }

        if ($fieldName == 'competition_difficulty') {
            $prompt .= "\n请根据以上信息，重新生成该院校该专业的竞争难度分析，要求：\n";
            $prompt .= "1. 分析报考人数、录取比例、分数线趋势\n";
            $prompt .= "2. 结合学生预估分数进行难度评估\n";
            $prompt .= "3. 分析该校在该专业领域的地位和声誉\n";
            $prompt .= "4. 字数控制在150-200字\n";
            $prompt .= "5. 直接返回分析内容，不需要JSON格式\n";
        } else if ($fieldName == 'suggestions') {
            $prompt .= "\n请根据以上信息，重新生成备考目标建议，要求：\n";
            $prompt .= "1. 针对学生当前分数给出具体的提升建议\n";
            $prompt .= "2. 结合院校录取分数线给出合理目标\n";
            $prompt .= "3. 字数控制在150-200字\n";
            $prompt .= "4. 直接返回建议内容，不需要JSON格式\n";
        }

        return $prompt;
    }

    /**
     * 构建高性价比院校推荐原因提示词
     *
     * @param array $reportInfo
     * @param array $allSchools
     * @param array $highRecommendSchools
     * @return string
     */
    private function buildHighRecommendPrompt($reportInfo, $allSchools, $highRecommendSchools)
    {
        $pos = mb_strpos($reportInfo['context'], "学校列表");
        $prompt = substr($reportInfo['context'], 0, $pos);

        $prompt .= "所有推荐院校列表：\n";
        foreach ($allSchools as $school) {
            $prompt .= "- {$school['school_name']}\n";
        }

        $prompt .= "\n高性价比推荐院校：\n";
        foreach ($highRecommendSchools as $school) {
            $prompt .= "- {$school['school_name']}\n";
        }

        $prompt .= "\n请重新分析为什么推荐这些高性价比院校，要求：\n";
        $prompt .= "1. 从录取难度、学科实力、地理位置、就业前景等维度分析\n";
        $prompt .= "2. 结合学生的预估分数和背景进行个性化分析\n";
        $prompt .= "3. 说明相比其他院校的优势所在\n";
        $prompt .= "4. 不少于300字\n";
        $prompt .= "5. 直接返回推荐原因，不需要JSON格式\n";
        $prompt .= "6. 输出格式：推荐院校：，{$school['school_name']}， 推荐原因：";

        return $prompt;
    }

    /**
     * 调用AI重新生成院校字段（流式版本）
     *
     * @param string $prompt
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     * @param TcpConnection $connection
     */
    private function callAIForRegenerateStream($prompt, $reportId, $schoolId, $fieldName, $connection)
    {
        try {
            // 获取千问配置
            $config = config('qianwen.remake');
            $baseUrl = config('qianwen.url');

            $appId = $config['appid'];
            $apiKey = $config['key'];

            $url = $baseUrl . 'apps/' . $appId . '/completion';

            // 构建请求数据
            $requestData = [
                'input' => [
                    'prompt' => $prompt
                ],
                'parameters' => [
                    'incremental_output' => true
                ]
            ];

            $dataString = json_encode($requestData);

            // 初始化cURL
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300);

            $fullContent = '';
            $sendContent = '';

            // 设置流式处理回调
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use ($connection, &$fullContent, &$sendContent, $reportId, $schoolId, $fieldName) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    if (strpos($line, 'data:') === 0) {
                        $jsonData = substr($line, 5);
                        try {
                            $decoded = json_decode($jsonData, true);
                            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['output']['text'])) {
                                $text = $decoded['output']['text'];
                                $sendContent .= $text;

                                Log::info("重新生成流式输出: " . $sendContent);

                                // 累积完整内容
                                $fullContent .= $sendContent;

                                // 发送数据块
                                $connection->send(new ServerSentEvents(['data' => $sendContent]));
                                $sendContent = "";
                            }
                        } catch (\Exception $e) {
                            Log::error('解析千问响应异常: ' . $e->getMessage());
                        }
                    }
                }
                return strlen($data);
            });

            // 执行请求
            $response = curl_exec($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($response === false || $status_code != 200) {
                throw new \Exception("API请求失败，状态码: $status_code");
            }

            // 保存生成的内容到数据库
            $this->saveRegeneratedContent($reportId, $schoolId, $fieldName, $fullContent);

            // 发送完成事件
            $connection->send(new ServerSentEvents([
                'event' => 'complete',
                'data' => '重新生成完成'
            ]));

        } catch (\Exception $e) {
            Log::error('调用AI重新生成失败: ' . $e->getMessage());
            $connection->send(new ServerSentEvents([
                'event' => 'error',
                'data' => json_encode(['error' => $e->getMessage()])
            ]));
        }
    }

    /**
     * 调用AI重新生成院校字段
     *
     * @param string $prompt
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     */
    private function callAIForRegenerate($prompt, $reportId, $schoolId, $fieldName)
    {
        try {
            // 获取千问配置
            $config = config('qianwen.remake');
            $baseUrl = config('qianwen.url');

            $appId = $config['appid'];
            $apiKey = $config['key'];

            $url = $baseUrl . 'apps/' . $appId . '/completion';

            // 构建请求数据
            $requestData = [
                'input' => [
                    'prompt' => $prompt
                ],
                'parameters' => [
                    'incremental_output' => true
                ]
            ];

            // 调用流式API
            $this->streamQianwenApiForRegenerate($url, $requestData, $apiKey, function($content) use ($reportId, $schoolId, $fieldName) {
                if ($content === 'done') {
                    // 流式输出结束，发送完成事件
                    echo "event: complete\n";
                    echo "data: 重新生成完成\n\n";
                    ob_flush();
                    flush();
                } else {
                    // 发送流式数据
                    echo "data: " . $content . "\n\n";
                    ob_flush();
                    flush();
                }
            }, $reportId, $schoolId, $fieldName);

        } catch (\Exception $e) {
            Log::error('调用AI重新生成失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 调用AI重新生成高性价比院校推荐原因（流式版本）
     *
     * @param string $prompt
     * @param int $reportId
     * @param TcpConnection $connection
     */
    private function callAIForHighRecommendStream($prompt, $reportId, $connection)
    {
        try {
            // 获取千问配置
            $config = config('qianwen.remake');
            $baseUrl = config('qianwen.url');

            $appId = $config['appid'];
            $apiKey = $config['key'];

            $url = $baseUrl . 'apps/' . $appId . '/completion';

            // 构建请求数据
            $requestData = [
                'input' => [
                    'prompt' => $prompt
                ],
                'parameters' => [
                    'incremental_output' => true
                ]
            ];

            $dataString = json_encode($requestData);

            // 初始化cURL
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300);

            $fullContent = '';
            $sendContent = '';

            // 设置流式处理回调
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use ($connection, &$fullContent, &$sendContent, $reportId) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    if (strpos($line, 'data:') === 0) {
                        $jsonData = substr($line, 5);
                        try {
                            $decoded = json_decode($jsonData, true);
                            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['output']['text'])) {
                                $text = $decoded['output']['text'];
                                $sendContent .= $text;

                                // 应用与streamAiRecommendation相同的验证逻辑
                                if(preg_match('/[A-O]/',$sendContent) && !preg_match('/[A-O]\./', $sendContent)){
                                    // 发现了A-O字母但还没有完整的"字母+点号"，继续扩展
                                    return strlen($data);
                                }
                                if(preg_match('/L\./', $sendContent) && strlen($sendContent) < 10){
                                    return strlen($data);
                                }

                                Log::info("重新生成高性价比推荐原因流式输出: " . $sendContent);

                                // 累积完整内容
                                $fullContent .= $sendContent;

                                // 发送数据块
                                $connection->send(new ServerSentEvents(['data' => $sendContent]));
                                $sendContent = "";
                            }
                        } catch (\Exception $e) {
                            Log::error('解析千问响应异常: ' . $e->getMessage());
                        }
                    }
                }
                return strlen($data);
            });

            // 执行请求
            $response = curl_exec($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($response === false || $status_code != 200) {
                throw new \Exception("API请求失败，状态码: $status_code");
            }

            // 保存生成的内容到数据库
            $this->saveRegeneratedContent($reportId, -1, 'high_recommend_reason', $fullContent);

            // 发送完成事件
            $connection->send(new ServerSentEvents([
                'event' => 'complete',
                'data' => '重新生成完成'
            ]));

        } catch (\Exception $e) {
            Log::error('调用AI重新生成高性价比推荐原因失败: ' . $e->getMessage());
            $connection->send(new ServerSentEvents([
                'event' => 'error',
                'data' => json_encode(['error' => $e->getMessage()])
            ]));
        }
    }

    /**
     * 调用AI重新生成高性价比院校推荐原因
     *
     * @param string $prompt
     * @param int $reportId
     */
    private function callAIForHighRecommend($prompt, $reportId)
    {
        try {
            // 获取千问配置
            $config = config('qianwen.remake');
            $baseUrl = config('qianwen.url');

            $appId = $config['appid'];
            $apiKey = $config['key'];

            $url = $baseUrl . 'apps/' . $appId . '/completion';

            // 构建请求数据
            $requestData = [
                'input' => [
                    'prompt' => $prompt
                ],
                'parameters' => [
                    'incremental_output' => true
                ]
            ];

            // 调用流式API
            $this->streamQianwenApiForRegenerate($url, $requestData, $apiKey, function($content) use ($reportId) {
                if ($content === 'done') {
                    // 流式输出结束，发送完成事件
                    echo "event: complete\n";
                    echo "data: 重新生成完成\n\n";
                    ob_flush();
                    flush();
                } else {
                    // 发送流式数据
                    echo "data: " . $content . "\n\n";
                    ob_flush();
                    flush();
                }
            }, $reportId, -1, 'high_recommend_reason');

        } catch (\Exception $e) {
            Log::error('调用AI重新生成高性价比推荐原因失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 流式调用千问API（用于重新生成）
     *
     * @param string $url
     * @param array $requestData
     * @param string $apiKey
     * @param callable $callback
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     */
    private function streamQianwenApiForRegenerate($url, $requestData, $apiKey, $callback, $reportId, $schoolId, $fieldName)
    {
        try {
            $dataString = json_encode($requestData);

            // 初始化cURL
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $dataString);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'X-DashScope-SSE: enable'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300);

            $fullContent = '';

            // 设置流式处理回调
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use ($callback, &$fullContent) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    if (strpos($line, 'data:') === 0) {
                        $jsonData = substr($line, 5);
                        try {
                            $decoded = json_decode($jsonData, true);
                            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['output']['text'])) {
                                $text = $decoded['output']['text'];
                                $fullContent .= $text;
                                $callback($text);
                            }
                        } catch (\Exception $e) {
                            Log::error('解析千问响应异常: ' . $e->getMessage());
                        }
                    }
                }
                return strlen($data);
            });

            // 执行请求
            $response = curl_exec($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($response === false || $status_code != 200) {
                throw new \Exception("API请求失败，状态码: $status_code");
            }

            // 保存生成的内容到数据库
            $this->saveRegeneratedContent($reportId, $schoolId, $fieldName, $fullContent);

            $callback('done');

        } catch (\Exception $e) {
            Log::error('千问流式API调用异常: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 保存重新生成的内容到数据库
     *
     * @param int $reportId
     * @param int $schoolId
     * @param string $fieldName
     * @param string $content
     */
    private function saveRegeneratedContent($reportId, $schoolId, $fieldName, $content)
    {
        try {
            //ai 生成的内容 不在修改编辑时间
            $updateData = [];

            if ($fieldName == 'competition_difficulty') {
                $updateData['competition_difficulty'] = $content;
            } else if ($fieldName == 'suggestions') {
                $updateData['suggestions'] = $content;
            } else if ($fieldName == 'high_recommend_reason') {
                $updateData['reason_recommendation'] = $content;
                $updateData['high_recommend_reason'] = $content;
            }

            if ($schoolId == -1) {
                // 高性价比院校推荐原因
               ReportInfo::where('report_id', $reportId)
                    ->where('is_high_recommend', 1)
                    ->update($updateData);
            } else {
                // 普通院校字段
                ReportInfo::where('report_id', $reportId)
                    ->where('school_id', $schoolId)
                    ->update($updateData);
            }

            Log::info('重新生成的内容已保存到数据库', [
                'report_id' => $reportId,
                'school_id' => $schoolId,
                'field_name' => $fieldName
            ]);

        } catch (\Exception $e) {
            Log::error('保存重新生成内容失败: ' . $e->getMessage());
        }
    }
}