<template>
  <div class="container">
    <div class="credits-info">
      <div>
        <span class="credits-text">报告数：</span>
        <span class="credits-count">200</span>
        <span class="credits-count">人次</span>
      </div>

      <div class="button-group">
        <div class="action-btn" @click="previewReport">预览报告</div>
        <div class="action-btn" @click="exportReport">导出报告</div>
        <div class="action-btn">发送</div>
      </div>
    </div>

    <div class="generate-container">
      <div class="report-container">
        <div class="steps">
          <!-- 第一步：个人基础信息 -->
          <div class="step-section">
            <div class="step-header">
              <div class="step-title">第一部分：个人基础信息</div>
            </div>

            <div class="step-content">
              <div class="step-num-tag">
                <span>01</span>
                <div class="tag-text">个人基础信息</div>
              </div>

              <div class="form-grid">
                <div class="form-item">
                  <div class="item-label">学员姓名</div>
                  <el-input
                    disabled
                    v-model="reportForm.name"
                    placeholder="请输入学员姓名"
                  ></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">性别</div>
                  <el-select
                    disabled
                    v-model="reportForm.sex"
                    class="full-width"
                  >
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="2"></el-option>
                    <el-option label="其他" value="3"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">本科院校</div>
                  <el-select
                    disabled
                    v-model="reportForm.undergraduateSchool"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchCollege"
                    :loading="collegeSearchLoading"
                    class="full-width"
                    @change="handleCollegeChange"
                  >
                    <el-option
                      v-for="item in collegeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">本科专业</div>
                  <el-select
                    disabled
                    v-model="reportForm.undergraduateMajor"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchMajor"
                    :loading="majorSearchLoading"
                    class="full-width"
                    :disabled="!reportForm.undergraduateSchool"
                  >
                    <el-option
                      v-for="item in majorOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">学科门类</div>
                  <el-select
                    v-model="reportForm.disciplineCategory"
                    class="full-width"
                    @change="handleDisciplineCategoryChange"
                    disabled="true"
                  >
                    <el-option
                      v-for="item in disciplineCategoryOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
                <div class="form-item">
                  <div class="item-label">一级学科</div>
                  <el-select
                    v-model="reportForm.firstLevelDiscipline"
                    class="full-width"
                    disabled="true"
                  >
                    <el-option
                      v-for="item in firstLevelDisciplineOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">目标专业</div>
                  <el-input
                    disabled
                    v-model="reportForm.targetMajor"
                  ></el-input>
                </div>
                <div class="form-item">
                  <div class="item-label">专业代码</div>
                  <el-input disabled v-model="reportForm.majorCode"></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">联系方式</div>
                  <el-input disabled v-model="reportForm.phone"></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">考研年份</div>
                  <el-select
                    disabled
                    v-model="reportForm.examYear"
                    class="full-width"
                  >
                    <el-option label="2027" value="2027"></el-option>
                    <el-option label="2026" value="2026"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">跨专业</div>
                  <el-select
                    disabled
                    v-model="reportForm.isMultiDisciplinary"
                    class="full-width"
                  >
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="2"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">培养方式</div>
                  <el-select
                    disabled
                    v-model="reportForm.educationalStyle"
                    class="full-width"
                  >
                    <el-option label="全日制" value="0"></el-option>
                    <el-option label="非全日制" value="1"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二步：本科成绩情况 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-container">
                <div class="step-num-tag">
                  <span>02</span>
                  <div class="tag-text">本科成绩情况</div>
                </div>
                <div class="create-btn" v-if="false">
                  <el-button
                    type="primary"
                    class="action-button"
                    @click="openAddScoreDialog"
                    >创建</el-button
                  >
                </div>
              </div>

              <div class="score-grid">
                <div class="score-row">
                  <div
                    class="score-item"
                    v-for="item in scoreInfo"
                    :key="item.id"
                    :draggable="true"
                    @dragstart="handleDragStart($event, item)"
                    @dragover.prevent="handleDragOver($event)"
                    @drop="handleDrop($event, item)"
                    @dragend="handleDragEnd"
                  >
                    <div class="score-label">{{ item.title }}</div>
                    <div class="score-input-container">
                      <el-input
                        readonly
                        v-model="item.score"
                        placeholder="140"
                      ></el-input>
                    </div>
                    <!--                  <el-icon-->
                    <!--                    @click="delScoreInfo(item.id)"-->
                    <!--                    class="score-close"-->
                    <!--                    color="#1BB394"-->
                    <!--                    size="14"-->
                    <!--                  >-->
                    <!--                    <Close />-->
                    <!--                  </el-icon>-->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三步：英语基础 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-num-tag">
                <span>03</span>
                <div class="tag-text">英语基础</div>
              </div>

              <div class="english-grid">
                <div class="english-row">
                  <div class="english-item">
                    <div class="english-label">高考英语成绩</div>
                    <el-input
                      readonly
                      v-model="reportForm.englishScore"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">大学四级</div>
                    <el-input
                      readonly
                      v-model="reportForm.cet4"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">大学六级</div>
                    <el-input
                      readonly
                      v-model="reportForm.cet6"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">托福</div>
                    <el-input
                      readonly
                      v-model="reportForm.tofelScore"
                      class="full-width"
                    ></el-input>
                  </div>
                </div>

                <div class="english-row">
                  <div class="english-item">
                    <div class="english-label">雅思</div>
                    <el-input
                      readonly
                      v-model="reportForm.ieltsScore"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">英语能力</div>
                    <el-select
                      disabled
                      v-model="reportForm.englishLevel"
                      class="full-width"
                    >
                      <el-option label="一般" value="average"></el-option>
                      <el-option label="良好" value="good"></el-option>
                      <el-option label="优秀" value="excellent"></el-option>
                    </el-select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第四步：目标院校梯度 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-num-tag">
                <span>04</span>
                <div class="tag-text">目标院校梯度</div>
              </div>

              <div class="school-grid">
                <div class="form-item">
                  <div class="item-label">地区倾向</div>
                  <el-select
                    disabled
                    v-model="reportForm.region"
                    multiple
                    class="full-width"
                  >
                    <el-option label="A区" value="A区"></el-option>
                    <el-option label="B区" value="B区"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">省份选择</div>
                  <el-select
                    disabled
                    v-model="reportForm.intendedSchools"
                    multiple
                    class="full-width"
                  >
                    <el-option
                      v-for="item in filteredProvinceData"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">梦校</div>
                  <el-select
                    disabled
                    v-model="reportForm.targetSchoolName"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchDreamSchool"
                    :loading="dreamSchoolSearchLoading"
                    class="full-width"
                    @change="
                      (val) => {
                        if (val) {
                          const selected = dreamSchoolOptions.find(
                            (item) => item.value === val
                          );
                          if (selected) {
                            reportForm.targetSchoolName = selected.label;
                          }
                        } else {
                          reportForm.targetSchoolName = '';
                        }
                      }
                    "
                  >
                    <el-option
                      v-for="item in dreamSchoolOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">院校层次</div>
                  <el-select
                    disabled
                    v-model="reportForm.schoolLevel"
                    multiple
                    class="full-width"
                  >
                    <el-option label="985" value="985"></el-option>
                    <el-option label="211" value="211"></el-option>
                    <el-option label="双一流" value="双一流"></el-option>
                    <el-option label="双非" value="双非"></el-option>
                  </el-select>
                </div>

                <div class="form-item wide-item">
                  <div class="item-label">专业课指定参考书</div>
                  <el-input
                    readonly
                    v-model="reportForm.referenceBooks"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>

          <!-- 第五步：考研成绩预估 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-num-tag">
                <span>05</span>
                <div class="tag-text">考研成绩预估</div>
              </div>

              <div class="score-table">
                <div class="table-header">
                  <div class="th-cell">政治</div>
                  <div class="th-cell">
                    <el-select
                      disabled
                      class="sel-no-border center-select"
                      v-model="englishMajor"
                      size="large"
                      popper-class="center-select-dropdown"
                    >
                      <el-option
                        v-for="item in EnglishMajorOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div class="th-cell self-score">
                    <el-select
                      disabled
                      v-model="mathMajor"
                      size="large"
                      class="sel-no-border center-select"
                      popper-class="center-select-dropdown"
                    >
                      <el-option
                        v-for="item in MathMajorOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div class="th-cell">专业课</div>
                  <div class="th-cell">总分</div>
                </div>
                <div class="table-row-score">
                  <div class="td-cell">
                    <el-input
                      readonly
                      v-model="reportForm.politics"
                      class="table-input"
                      @input="calculateTotalScore"
                    ></el-input>
                  </div>
                  <div class="td-cell">
                    <el-input
                      v-model="reportForm.englishType"
                      class="table-input"
                      @input="calculateTotalScore"
                    ></el-input>
                  </div>
                  <div class="td-cell">
                    <el-input
                      readonly
                      v-model="reportForm.mathType"
                      class="table-input"
                      @input="calculateTotalScore"
                    ></el-input>
                  </div>
                  <div class="td-cell">
                    <el-input
                      readonly
                      v-model="reportForm.professional"
                      class="table-input"
                      @input="calculateTotalScore"
                    ></el-input>
                  </div>
                  <div class="td-cell">
                    <el-input
                      readonly
                      v-model="reportForm.totalScore"
                      class="table-input"
                    ></el-input>
                  </div>
                </div>
              </div>

              <div class="personal-demands">
                <div class="demands-label">个性化需求</div>
                <el-input
                  readonly
                  v-model="reportForm.personalNeeds"
                  type="textarea"
                  :rows="1"
                  class="demands-input"
                ></el-input>
              </div>

              <div class="expertise-advice">
                <div class="advice-label">薄弱模块</div>
                <el-input
                  readonly
                  v-model="reportForm.weakModules"
                  type="textarea"
                  :rows="1"
                  class="advice-input"
                ></el-input>
              </div>
            </div>
          </div>

          <!-- AI图标旋转动画 -->
          <!-- <div class="ai-animation-container" v-if="showCreateBtn">
          <div
            class="ai-icon-wrapper"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
            @click="toggleAIOverlay"
            v-show="!showContent"
          >
            <div
              class="ai-icon-layer outer"
              :class="{
                'animate-outer': isAnimating,
                'hover-effect': isHovering,
              }"
            ></div>
            <div
              class="ai-icon-layer middle"
              :class="{ 'animate-middle': isAnimating }"
            ></div>
            <div
              class="ai-icon-layer inner"
              :class="{ 'animate-inner': isAnimating }"
            ></div>
          </div>
        </div> -->

          <!-- AI遮罩层 -->
          <div class="ai-overlay" v-if="showAIOverlay" @click="toggleAIOverlay">
            <div class="ai-overlay-content">
              <div class="ai-large-icon-wrapper">
                <div
                  class="ai-icon-layer outer"
                  :class="{ 'animate-outer': true }"
                ></div>
                <div
                  class="ai-icon-layer middle"
                  :class="{ 'animate-middle': true }"
                ></div>
                <div
                  class="ai-icon-layer inner"
                  :class="{ 'animate-inner': true }"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <!-- 专业分析组件 -->
        <MajorAnalysis
          ref="majorAnalysisRef"
          :national-line-data="nationalLineData"
          :report-data="{
            targetMajorName: reportForm.targetMajor,
            majorCode: reportForm.majorCode,
            firstLevelSubject: getFirstLevelSubject(reportForm.majorCode),
          }"
          :visible="showMajorAnalysis"
        />
        <report-content
          ref="reportContentElement"
          @updateOverlay="handleOverlayUpdate"
        ></report-content>
        <WeakModuleAnalysis ref="weakModuleAnalysisRef"></WeakModuleAnalysis>
      </div>

      <!-- 预览弹窗 -->
      <el-dialog
        v-model="previewDialogVisible"
        title="报告预览"
        width="90%"
        top="5vh"
        :close-on-click-modal="false"
        class="preview-dialog"
      >
        <div class="preview-container" ref="previewContainer">
          <div
            v-if="previewLoading"
            class="preview-loading"
            v-loading="previewLoading"
            element-loading-text="正在生成预览..."
          ></div>
          <div
            v-else-if="previewContent"
            v-html="previewContent"
            class="preview-content"
          ></div>
          <div v-else class="preview-empty">
            <p>暂无预览内容</p>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="previewDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="generatePDFFromPreview"
              >生成PDF</el-button
            >
          </div>
        </template>
      </el-dialog>
      <el-dialog
        title="预览报告"
        v-model="previewPdfDialogVisible"
        width="860px"
        :close-on-click-modal="false"
      >
        <preview-pdf-new ref="previewPdfRef"></preview-pdf-new>
      </el-dialog>

      <!-- 添加成绩对话框 -->
      <el-dialog
        title="添加成绩项"
        v-model="dialogVisible"
        width="30%"
        :close-on-click-modal="false"
      >
        <el-form :model="newScoreForm" label-width="80px">
          <el-form-item label="科目名称">
            <el-input v-model="newScoreForm.title"></el-input>
          </el-form-item>
          <el-form-item label="成绩">
            <el-input v-model="newScoreForm.score"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              style="background-color: #1bb394"
              @click="addNewScore"
              >确认</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  nextTick,
  watch,
  computed,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getDisciplineCategories,
  getFirstLevelDisciplines,
  getStudentDetail,
} from "@/api/student";
import {
  searchSchool,
  searchMajor,
  searchTargetMajor,
  getNationalLineData,
} from "@/api/school";
import { getReportBasicInfo } from "@/api/report";
import provinceData from "@/utils/province.json";
import * as echarts from "echarts";
import reportContent from "./components/content_edit.vue";
import MajorAnalysis from "./components/MajorAnalysis.vue";
import WeakModuleAnalysis from "./components/WeakModuleAnalysisEdit.vue";
import PreviewPdf from "./components/PreviewPdf.vue";
import PreviewPdfNew from "./components/PreviewPdfNew.vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const weakModuleAnalysisRef = ref(null);
const reportContentElement = ref(null);
let showCreateBtn = ref(true);
// 加载状态
const loading = ref(false);
const collegeSearchLoading = ref(false);
const collegeOptions = ref([]);
const collegeSearchTimeout = ref(null);
const collegeSearchCache = {};

const majorSearchLoading = ref(false);
const majorOptions = ref([]);
const majorSearchTimeout = ref(null);
const majorSearchCache = {};

const targetMajorSearchLoading = ref(false);
const targetMajorOptions = ref([]);
const targetMajorSearchTimeout = ref(null);
const targetMajorSearchCache = {};

// 学科分类相关数据
const disciplineCategoryOptions = ref([]);
const firstLevelDisciplineOptions = ref([]);
const secondLevelDisciplineOptions = ref([]);

const firstLevelDisciplineTmp = ref("");

// 梦校搜索相关
const dreamSchoolSearchLoading = ref(false);
const dreamSchoolOptions = ref([]);
const dreamSchoolSearchTimeout = ref(null);
const dreamSchoolSearchCache = {};
const showMajorAnalysis = ref(false);
// 表单数据
const reportForm = reactive({
  // 第一步：个人基础信息
  name: "",
  sex: "1", // 1-男, 2-女
  undergraduateSchool: "",
  undergraduateMajor: "",
  targetMajor: "",
  majorCode: "",
  phone: "",
  examYear: "2027",
  isMultiDisciplinary: "1", // 1-是, 2-否
  educationalStyle: "1", //培养方式

  // 第二步：本科成绩情况
  gaokaoScore: "",
  mathScore1: "",
  mathScore2: "",
  statScore: "",
  linearScore: "",
  majorScore1: "",
  majorScore2: "",
  majorScore3: "",

  // 第三步：英语基础
  englishScore: "",
  cet4: "",
  cet6: "",
  tofelScore: "",
  ieltsScore: "",
  englishLevel: "",

  // 第四步：目标院校梯度
  region: [], // 保持数组，支持多选，默认包含所有区域
  intendedSchools: [], // 初始为空数组，会根据选择的区域动态填充
  targetSchool: "",
  targetSchoolName: "", // 梦校名称
  schoolLevel: "",
  referenceBooks: "",

  // 第五步：考研成绩预估
  politics: "",
  englishType: "",
  mathType: "",
  professional: "",
  totalScore: "",
  personalNeeds: "",
  weakModules: "",
});

// 预览功能相关
const previewPdfDialogVisible = ref(false);

// 根据专业代码获取一级学科
const getFirstLevelSubject = (code) => {
  if (!code || code.length < 2) return "";

  // 根据专业代码前两位确定一级学科
  const subjectCode = code.substring(0, 2);

  console.log(subjectCode);
  const subjectMap = {
    "01": "哲学",
    "02": "经济学",
    "03": "法学",
    "04": "教育学",
    "05": "文学",
    "06": "历史学",
    "07": "理学",
    "08": "工学",
    "09": "农学",
    10: "医学",
    11: "军事学",
    12: "管理学",
    13: "艺术学",
  };

  return subjectMap[subjectCode] || "";
};

let scoreInfo = ref([]);
let showContent = ref(false);

// 预览功能相关
const previewDialogVisible = ref(false);
const previewLoading = ref(false);
const previewContent = ref("");
const previewContainer = ref(null);
const nationalLineData = ref(null);

// 添加成绩相关
const dialogVisible = ref(false);
const newScoreForm = reactive({
  title: "",
  score: "",
});

const handleOverlayUpdate = (value) => {
  showAIOverlay.value = value;
};

// 拖拽相关状态
const draggedItem = ref(null);

// 拖拽开始
const handleDragStart = (event, item) => {
  draggedItem.value = item;
  event.dataTransfer.effectAllowed = "move";
  // 设置拖拽时的半透明效果
  event.target.style.opacity = "0.5";
};

// 拖拽经过
const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = "move";

  // 添加拖拽经过的视觉反馈
  const targetElement = event.currentTarget;
  if (
    targetElement &&
    draggedItem.value &&
    targetElement !== draggedItem.value
  ) {
    targetElement.classList.add("drag-over");
  }
};

// 拖拽放置
const handleDrop = (event, targetItem) => {
  event.preventDefault();

  // 移除所有元素的拖拽效果类
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });

  if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
    return;
  }

  // 找到拖拽项和目标项的索引
  const dragIndex = scoreInfo.value.findIndex(
    (item) => item.id === draggedItem.value.id
  );
  const targetIndex = scoreInfo.value.findIndex(
    (item) => item.id === targetItem.id
  );

  if (dragIndex < 0 || targetIndex < 0) {
    return;
  }

  // 重新排序
  const newScoreInfo = [...scoreInfo.value];
  const [removed] = newScoreInfo.splice(dragIndex, 1);
  newScoreInfo.splice(targetIndex, 0, removed);

  // 更新成绩列表
  scoreInfo.value = newScoreInfo;

  // 清除拖拽状态
  draggedItem.value = null;

  // 恢复透明度
  document.querySelectorAll(".score-item").forEach((el) => {
    el.style.opacity = "1";
  });

  ElMessage.success("排序已更新");
};

// 拖拽结束
const handleDragEnd = (event) => {
  // 恢复透明度
  event.target.style.opacity = "1";

  // 可以添加拖拽结束的视觉反馈
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });
};

// 打开添加成绩对话框
const openAddScoreDialog = () => {
  dialogVisible.value = true;
  // 重置表单
  newScoreForm.title = "";
  newScoreForm.score = "";
};

// 添加新成绩
const addNewScore = () => {
  // 验证表单
  if (!newScoreForm.title || !newScoreForm.score) {
    ElMessage.warning("科目名称和成绩不能为空");
    return;
  }

  // 生成新ID (取当前最大ID + 1)
  const maxId = Math.max(...scoreInfo.value.map((item) => item.id), 0);
  const newId = maxId + 1;

  // 添加到成绩列表
  scoreInfo.value.push({
    id: newId,
    title: newScoreForm.title,
    score: Number(newScoreForm.score) || newScoreForm.score,
  });

  // 关闭对话框
  dialogVisible.value = false;
  ElMessage.success("添加成功");
};

// 搜索本科院校
const remoteSearchCollege = (query) => {
  if (query !== "") {
    collegeSearchLoading.value = true;

    // 检查缓存中是否已有该查询结果
    if (collegeSearchCache[query]) {
      collegeOptions.value = collegeSearchCache[query];
      collegeSearchLoading.value = false;
      return;
    }

    // 延迟300ms，避免频繁请求
    clearTimeout(collegeSearchTimeout.value);
    collegeSearchTimeout.value = setTimeout(() => {
      searchSchool(query)
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 假设后端返回的数据格式是 {code: 0, data: [{id: 1, name: '中国农业大学'}, ...]}
            const options = res.data.map((item) => ({
              value: item.id.toString(),
              label: item.name,
            }));
            collegeOptions.value = options;
            // 添加到缓存
            collegeSearchCache[query] = options;
          } else {
            collegeOptions.value = [];
          }
        })
        .catch(() => {
          collegeOptions.value = [];
        })
        .finally(() => {
          collegeSearchLoading.value = false;
        });
    }, 300);
  } else {
    collegeOptions.value = [];
  }
};

//删除本科成绩
const delScoreInfo = (id) => {
  ElMessageBox.confirm("确定删除该成绩吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    closeOnPressEscape: false,
    showClose: false,
  })
    .then(() => {
      // 删除成绩
      scoreInfo.value = scoreInfo.value.filter((item) => item.id !== id);
      ElMessage.success("删除成功");
    })
    .catch(() => {});
};

// 搜索本科专业
const remoteSearchMajor = (query) => {
  if (!reportForm.undergraduateSchool) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  if (query !== "") {
    majorSearchLoading.value = true;

    // 生成缓存键，包含学校ID和查询词
    const cacheKey = `${reportForm.undergraduateSchool}_${query}`;

    // 检查缓存中是否已有该查询结果
    if (majorSearchCache[cacheKey]) {
      majorOptions.value = majorSearchCache[cacheKey];
      majorSearchLoading.value = false;
      return;
    }

    // 延迟300ms，避免频繁请求
    clearTimeout(majorSearchTimeout.value);
    majorSearchTimeout.value = setTimeout(() => {
      searchMajor(reportForm.undergraduateSchool, query)
        .then((res) => {
          if (res.code === 0 && res.data) {
            const options = res.data.map((item) => ({
              value: item.id.toString(),
              label: item.major_name,
            }));
            majorOptions.value = options;
            // 添加到缓存
            majorSearchCache[cacheKey] = options;
          } else {
            majorOptions.value = [];
          }
        })
        .catch(() => {
          majorOptions.value = [];
        })
        .finally(() => {
          majorSearchLoading.value = false;
        });
    }, 300);
  } else {
    majorOptions.value = [];
  }
};

// 搜索目标专业
const remoteSearchTargetMajor = (query) => {
  if (query !== "") {
    targetMajorSearchLoading.value = true;

    // 检查缓存中是否已有该查询结果
    if (targetMajorSearchCache[query]) {
      targetMajorOptions.value = targetMajorSearchCache[query];
      targetMajorSearchLoading.value = false;
      return;
    }

    // 延迟300ms，避免频繁请求
    clearTimeout(targetMajorSearchTimeout.value);
    targetMajorSearchTimeout.value = setTimeout(() => {
      searchTargetMajor(query)
        .then((res) => {
          if (res.code === 0 && res.data) {
            const options = res.data.map((item) => ({
              value: item.name,
              label: item.name,
              code: item.code,
            }));
            targetMajorOptions.value = options;
            // 添加到缓存
            targetMajorSearchCache[query] = options;
          } else {
            targetMajorOptions.value = [];
          }
        })
        .catch(() => {
          targetMajorOptions.value = [];
        })
        .finally(() => {
          targetMajorSearchLoading.value = false;
        });
    }, 300);
  } else {
    targetMajorOptions.value = [];
  }
};

// 搜索梦校
const remoteSearchDreamSchool = (query) => {
  if (!query) {
    dreamSchoolOptions.value = [];
    return;
  }

  dreamSchoolSearchLoading.value = true;
  console.log("搜索梦校:", query);

  // 检查缓存中是否已有该查询结果
  if (dreamSchoolSearchCache[query]) {
    dreamSchoolOptions.value = dreamSchoolSearchCache[query];
    dreamSchoolSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(dreamSchoolSearchTimeout.value);
  dreamSchoolSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("搜索梦校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          dreamSchoolOptions.value = options;
          // 添加到缓存
          dreamSchoolSearchCache[query] = options;

          // 如果选择了梦校，保存梦校名称
          if (reportForm.targetSchool) {
            const selectedSchool = options.find(
              (item) => item.value === reportForm.targetSchool
            );
            if (selectedSchool) {
              reportForm.targetSchoolName = selectedSchool.label;
            }
          }
        } else {
          dreamSchoolOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取梦校列表失败", err);
        dreamSchoolOptions.value = [];
      })
      .finally(() => {
        dreamSchoolSearchLoading.value = false;
      });
  }, 300);
};

// 监听本科院校变更
const handleCollegeChange = () => {
  // 清空专业选择
  reportForm.major = "";
  reportForm.targetMajor = "";
  // 清空专业选项
  majorOptions.value = [];
  targetMajorOptions.value = [];
};

// 监听目标专业变更
const handleTargetMajorChange = (value) => {
  // 根据选择的专业找到对应的专业代码
  const selectedMajor = targetMajorOptions.value.find(
    (item) => item.value === value
  );
  if (selectedMajor && selectedMajor.code) {
    // 设置专业代码
    reportForm.majorCode = selectedMajor.code;
  } else {
    // 清空专业代码
    reportForm.majorCode = "";
  }
};

// 提交数据
const handleSubmit = () => {
  ElMessage.success("报告提交成功！");
};

const EnglishMajorOptions = [
  { value: "0", label: "英语一" },
  { value: "1", label: "英语二" },
];
let englishMajor = ref("");
let mathMajor = ref("");
const MathMajorOptions = [
  { value: "0", label: "数学一" },
  { value: "1", label: "数学二" },
  { value: "2", label: "数学三" },
  { value: "3", label: "199管理类联考" },
  { value: "4", label: "396经济类联考" },
];

// 根据所选区域过滤省份数据
const filteredProvinceData = computed(() => {
  // 始终返回所有省份，不再根据区域过滤
  // 这样可以确保所有省份都可见，无论选择了哪个区域
  return provinceData;
});

// 获取英语类型标签
const getEnglishLabel = (value) => {
  if (!value) return "英语一";
  const option = EnglishMajorOptions.find((item) => item.value === value);
  return option ? option.label : "英语一";
};

// 获取数学类型标签
const getMathLabel = (value) => {
  if (!value) return "数学一";
  const option = MathMajorOptions.find((item) => item.value === value);
  return option ? option.label : "数学一";
};

// 旋转动画相关
const isAnimating = ref(false);
const isHovering = ref(false);
const showAIOverlay = ref(false);

// 处理鼠标悬停
const handleMouseEnter = () => {
  isHovering.value = true;
};

// 处理鼠标离开
const handleMouseLeave = () => {
  isHovering.value = false;
};

// 计算总分
const calculateTotalScore = () => {
  const politics = parseFloat(reportForm.politics) || 0;
  const english = parseFloat(reportForm.englishType) || 0;
  const math = parseFloat(reportForm.mathType) || 0;
  const professional = parseFloat(reportForm.professional) || 0;

  reportForm.totalScore = (politics + english + math + professional).toString();
};

// 切换遮罩层显示状态
const toggleAIOverlay = async () => {
  // 在显示AI内容前验证表单
  console.log(reportForm);
  // 验证通过，继续执行原有逻辑
  showAIOverlay.value = !showAIOverlay.value;

  if (showAIOverlay.value) {
    // 显示遮罩层时触发动画
    isAnimating.value = true;

    // 创建全局变量，用于传递专业名称、代码和一级学科给content.vue
    window.reportData = {
      targetMajorName: reportForm.targetMajor,
      majorCode: reportForm.majorCode,
      firstLevelSubject: getFirstLevelSubject(reportForm.majorCode), // 根据专业代码获取一级学科
    };

    // // 根据专业代码获取一级学科
    // function getFirstLevelSubject(code) {
    //   let otherCode = code.split(',')
    //   console.log(otherCode);
    //   if (!otherCode[0] || otherCode[0].length < 2) return "";
    //
    //   // 根据专业代码前两位确定一级学科
    //   const subjectCode = otherCode[0].substring(0, 2);
    //   const subjectMap = {
    //     1: "哲学",
    //     2: "经济学",
    //     3: "法学",
    //     4: "教育学",
    //     5: "文学",
    //     6: "历史学",
    //     7: "理学",
    //     8: "工学",
    //     9: "农学",
    //     10: "医学",
    //     11: "军事学",
    //     12: "管理学",
    //     13: "艺术学",
    //     14: "交叉学科"
    //   };
    //
    //   console.log('----------');
    //   console.log(firstLevelDiscipline.value)
    //   return subjectMap[subjectCode] || "";
    // }

    try {
      const reportId = reportUrlId.value;

      //关闭遮罩层并显示内容
      showAIOverlay.value = true;
      reportContentElement.value.show();
      // 如果有报告ID，调用AI推荐接口
      if (reportId) {
        console.log("设置报告ID并加载AI推荐:", reportId);
        weakModuleAnalysisRef.value.fetchStudyPlan(reportId);
        await reportContentElement.value.setReportId(reportId);

        showCreateBtn.value = false;

        // 获取国家线数据
        if (reportForm.majorCode) {
          console.log("开始获取国家线数据...");
          console.log(reportForm.majorCode);
          try {
            let otherCodeArr = reportForm.majorCode.split(",");
            console.log(otherCodeArr);
            // 根据专业代码前两位确定一级学科
            let majorCodeStr = otherCodeArr[0].substring(0, 2);
            reportForm.disciplineCategory = parseInt(majorCodeStr);
            firstLevelDisciplineTmp.value = otherCodeArr[0].substring(0, 4);
            const nationalLineRes = await getNationalLineData(
              firstLevelDisciplineTmp.value
            );
            //
            handleDisciplineCategoryChange(reportForm.disciplineCategory);

            if (nationalLineRes.code === 0 && nationalLineRes.data) {
              nationalLineData.value = nationalLineRes.data;
              showMajorAnalysis.value = true;
              // 将数据传递给content组件
            } else {
              console.error("获取国家线数据失败:", nationalLineRes.msg);
              ElMessage.warning(
                "获取国家线数据失败: " + (nationalLineRes.msg || "未知错误")
              );
            }
          } catch (nationalLineError) {
            console.error("获取国家线数据异常:", nationalLineError);
            ElMessage.error("获取国家线数据异常，将使用默认数据");
          }
        } else {
          console.warn("缺少专业代码，无法获取国家线数据");
          ElMessage.warning("缺少专业代码，无法获取国家线数据");
        }
      }
    } catch (error) {
      console.error("处理过程中发生错误:", error);
      ElMessage.error("处理失败: " + (error.message || "未知错误"));

      // 即使出错也关闭遮罩层并显示内容
      showAIOverlay.value = false;
      reportContentElement.value.show();
    }
  }
};

// 处理窗口大小调整
const handleResize = () => {
  if (chartA) {
    chartA.resize();
  }
  if (chartB) {
    chartB.resize();
  }
};
const reportUrlId = ref("");
const studentId = ref("");
onMounted(() => {
  reportUrlId.value = route.params.id;
  studentId.value = route.params.student_id;

  handleStudentSelected();

  nextTick(() => {
    // window.addEventListener("resize", handleResize);
    // // 添加自定义事件监听器，用于接收学员选择事件
    // window.addEventListener("student-selected", handleStudentSelected);
    // 页面加载时检查是否有预选的学员数据
    // const selectedStudentData = localStorage.getItem("selectedStudent");
    // if (selectedStudentData) {
    //   try {
    //     const studentData = JSON.parse(selectedStudentData);
    //     // 填充表单数据
    //     fillFormWithStudentData(studentData);
    //     // 清除localStorage中的数据，避免下次进入页面时自动填充
    //     localStorage.removeItem("selectedStudent");
    //   } catch (error) {
    //     console.error("解析预选学员数据失败:", error);
    //   }
    // }
  });
  loadDisciplineCategories();
});

// 加载学科门类列表
const loadDisciplineCategories = async () => {
  try {
    const res = await getDisciplineCategories();
    if (res.code === 0) {
      disciplineCategoryOptions.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取学科门类失败");
    }
  } catch (error) {
    console.error("获取学科门类失败", error);
    ElMessage.error("获取学科门类失败，请稍后重试");
  }
};
// 处理学科门类变更
const handleDisciplineCategoryChange = async (value) => {
  console.log("学科门类变更:", value);

  if (value) {
    try {
      const res = await getFirstLevelDisciplines(value);
      if (res.code === 0) {
        firstLevelDisciplineOptions.value = res.data;
        let arr = firstLevelDisciplineOptions.value.filter((item) => {
          if (item.value == firstLevelDisciplineTmp.value) {
            return item;
          }
        });
        reportForm.firstLevelDiscipline = arr[0].id;
        console.log(arr);
      } else {
        ElMessage.error(res.msg || "获取一级学科失败");
      }
    } catch (error) {
      console.error("获取一级学科失败", error);
      ElMessage.error("获取一级学科失败，请稍后重试");
    }
  }
};
// 清理资源
onBeforeUnmount(() => {
  // window.removeEventListener("resize", handleResize);
  window.removeEventListener("student-selected", handleStudentSelected);
});

// 处理学员选择事件
const handleStudentSelected = async () => {
  try {
    console.log("开始加载学员基本信息，报告ID:", reportUrlId.value);
    const response = await getReportBasicInfo(reportUrlId.value);
    console.log("学员基本信息API响应:", response);

    if (response.code === 0 && response.data) {
      const { student_info, report_info } = response.data;

      // 更新reportForm数据
      if (student_info) {
        reportForm.name = student_info.name || "";
        reportForm.sex = student_info.sexText || "";
        reportForm.undergraduateSchool =
          student_info.undergraduateSchoolName || "";
        reportForm.undergraduateMajor =
          student_info.undergraduateMajorName || "";
        reportForm.targetMajor = report_info.target_major || "";

        reportForm.phone = student_info.phone || "";
        reportForm.examYear = student_info.examYear || "";
        reportForm.isMultiDisciplinary = student_info.isCrossMajorText || "";
        reportForm.educationalStyle = student_info.educationalStyleText || "";
        reportForm.englishScore = student_info.englishScore || "";
        reportForm.cet4 = student_info.cet4 || "";
        reportForm.cet6 = student_info.cet6 || "";
        reportForm.tofelScore = student_info.tofelScore || "";
        reportForm.ieltsScore = student_info.ieltsScore || "";
        reportForm.undergraduateTranscript =
          student_info.undergraduateTranscript || [];
        reportForm.englishLevel = student_info.englishAbility || "";
      }

      // 更新报告相关信息
      if (report_info) {
        reportForm.politics = report_info.politics_score || "";
        reportForm.englishS = report_info.english_score || "";
        reportForm.englishType = report_info.english_type || "";
        reportForm.mathType = report_info.math_type || "";
        reportForm.professional = report_info.professional_score || "";
        reportForm.totalScore = report_info.total_score || "";
        reportForm.personalNeeds = report_info.personal_needs || "";
        reportForm.weakModules = report_info.weak_modules || "";
        reportForm.majorCode = report_info.major_code || "";

        // 院校信息
        reportForm.region = report_info.region_preference.split(",") || "";
        reportForm.intendedSchools =
          report_info.province_selection.split(",") || "";
        reportForm.targetSchoolName = report_info.dream_school || "";
        reportForm.schoolLevel = report_info.school_level.split(",") || "";
        reportForm.referenceBooks = report_info.reference_books || "";
      }
      toggleAIOverlay();
      fillFormWithStudentData(reportForm);
      console.log("学员基本信息加载成功，已更新reportForm");
    } else {
      console.warn("学员基本信息加载失败:", response.msg);
    }
  } catch (error) {
    ElMessage.error("获取报告失败");
    // router.push(`/student/detail/${student.id}`);
    console.error("加载学员基本信息失败:", error);
  }

  // getStudentDetail(studentId.value)
  //       .then((res) => {
  //         if (res.code === 0 && res.data) {
  //           const studentData =res.data;
  //           if (studentData) {
  //             // 打印完整的学员数据，用于调试
  //             console.log("选中的学员完整数据:", JSON.stringify(studentData, null, 2));
  //
  //             // 特别关注 targetProvinces 字段
  //             console.log(
  //               "targetProvinces 数据类型:",
  //               typeof studentData.targetProvinces
  //             );
  //             console.log("targetProvinces 值:", studentData.targetProvinces);
  //
  //             // 填充表单数据
  //
  //
  //             // 填充后检查表单中的省份数据
  //             console.log("填充后的省份数据:", reportForm.intendedSchools);
  //             console.log("填充后的区域数据:", reportForm.region);
  //           }
  //         } else {
  //           ElMessage.error(res.msg || "获取学员信息失败");
  //           router.push(`/student/detail/${student.id}`);
  //         }
  //       })
  //       .catch((err) => {
  //         console.error("获取学员详情失败", err);
  //         ElMessage.error("获取学员信息失败，跳转到详情页");
  //         router.push(`/student/detail/${student.id}`);
  //       });
};

// 填充表单数据
const fillFormWithStudentData = (studentData) => {
  console.log("填充学员数据到表单:", studentData);

  // 基本信息

  reportForm.sex =
    studentData.sex === 1 ? "1" : studentData.sex === 2 ? "2" : "";

  reportForm.undergraduateSchool =
    studentData.undergraduateSchoolName ||
    studentData.undergraduateSchool ||
    "";
  reportForm.undergraduateMajor =
    studentData.undergraduateMajorName || studentData.undergraduateMajor || "";
  reportForm.targetMajor =
    studentData.targetMajorName || studentData.targetMajor || "";

  // 考研信息
  if (studentData.examYear) {
    reportForm.examYear = studentData.examYear.toString();
  }

  reportForm.isMultiDisciplinary = studentData.isCrossMajor ? "1" : "2";

  reportForm.educationalStyle = studentData.educationalStyle ? "1" : "0";
  // reportForm.region =
  //         Array.isArray(reportForm.region) && reportForm.region.length > 0
  //                 ? reportForm.region[0]
  //                 : reportForm.region || "",

  // 清空当前选择的省份
  // reportForm.intendedSchools = [];
  // if (studentData.province_selection) {
  //   // 如果是数组，直接使用
  //   if (Array.isArray(studentData.province_selection)) {
  //     // 确保每个省份都存在于 provinceData 中
  //     const validProvinces = studentData.targetProvinces.filter((province) => {
  //       return provinceData.some((p) => p.value === province);
  //     });
  //
  //     if (validProvinces.length > 0) {
  //       reportForm.intendedSchools = validProvinces;
  //       console.log("有效的省份数据:", validProvinces);
  //     } else {
  //       console.warn("没有找到有效的省份数据");
  //     }
  //   }
  //   // 如果是字符串，尝试解析
  //   else if (typeof studentData.province_selection === "string") {
  //     // 首先尝试按逗号分隔
  //     const provinces = studentData.province_selection.split(",");
  //     // 过滤掉空字符串
  //     const validProvinces = provinces.filter((p) => {
  //       const trimmed = p.trim();
  //       return (
  //         trimmed !== "" && provinceData.some((prov) => prov.value === trimmed)
  //       );
  //     });
  //
  //     if (validProvinces.length > 0) {
  //       reportForm.intendedSchools = validProvinces;
  //       console.log("解析后的有效省份:", validProvinces);
  //     } else {
  //       console.warn("解析后没有找到有效的省份数据");
  //     }
  //   }
  // }
  console.log("---------");
  console.log(reportForm);
  // 确保省份数据在下拉框中可见
  console.log("设置省份选择:", reportForm.intendedSchools);
  // reportForm.region = studentData.targetRegion;
  // const intendedSchools = reportForm.intendedSchools;
  // 使用 nextTick 确保在 DOM 更新后再设置省份
  // nextTick(() => {
  //   reportForm.intendedSchools = intendedSchools;
  // });
  // reportForm.targetSchool = studentData.targetSchool || "";
  // reportForm.targetSchoolName = studentData.targetSchoolName || "";

  // 如果有梦校ID但没有梦校名称，添加到dreamSchoolOptions中
  if (reportForm.targetSchool && reportForm.targetSchoolName) {
    dreamSchoolOptions.value = [
      {
        value: reportForm.targetSchool,
        label: reportForm.targetSchoolName,
      },
    ];
  }
  reportForm.schoolLevel = studentData.schoolLevel || "";
  reportForm.referenceBooks = studentData.referenceBooks || "";

  // 成绩情况
  // 使用 undergraduateTranscript 字段
  if (studentData.undergraduateTranscript) {
    try {
      let transcript = studentData.undergraduateTranscript;
      if (typeof transcript === "string") {
        transcript = JSON.parse(transcript);
      }

      if (Array.isArray(transcript) && transcript.length > 0) {
        // 创建新的成绩项
        scoreInfo.value = transcript.map((score, index) => ({
          id: index + 1,
          title: score.title || score.name || `课程${index + 1}`,
          score: score.score || "",
        }));
      }
    } catch (error) {
      console.error("解析undergraduateTranscript失败:", error);
    }
  }

  // 如果没有任何成绩数据，添加默认的成绩项
  if (scoreInfo.value.length === 0) {
    scoreInfo.value = [
      { id: 1, title: "高数(上)", score: "" },
      { id: 2, title: "高数(下)", score: "" },
      { id: 3, title: "概率论", score: "" },
      { id: 4, title: "线性代数", score: "" },
    ];
  }

  // 考研预估
  reportForm.politics = studentData.politics || "";
  const typeEnglishValue = EnglishMajorOptions.find(
    (item) => item.label == studentData.englishType
  );
  // 设置英语类型下拉框
  englishMajor.value = typeEnglishValue ? typeEnglishValue.value : "0";

  // 设置英语预估分数
  reportForm.englishType = studentData.englishS || "";

  const typeMathValue = MathMajorOptions.find(
    (item) => item.label == studentData.mathType
  );
  // 设置数学类型下拉框
  mathMajor.value = typeMathValue ? typeMathValue.value : "0";

  // 设置数学预估分数
  reportForm.mathType = studentData.mathScore || "";

  // 设置专业课预估分数
  reportForm.professional = studentData.professionalScore || "";

  // 设置总分
  reportForm.totalScore = studentData.totalScore || "";

  // 手动触发计算总分
  calculateTotalScore();

  // 打印调试信息
  console.log("填充考研预估成绩:", {
    政治: reportForm.politics,
    英语类型: englishMajor.value,
    英语分数: reportForm.englishType,
    数学类型: mathMajor.value,
    数学分数: reportForm.mathType,
    专业课分数: reportForm.professional,
    总分: reportForm.totalScore,
  });

  // 自动计算总分（如果有政治、英语、数学和专业课的分数）
  if (
    reportForm.politics &&
    reportForm.englishType &&
    reportForm.mathType &&
    reportForm.professional
  ) {
    const politics = parseFloat(reportForm.politics) || 0;
    const english = parseFloat(reportForm.englishType) || 0;
    const math = parseFloat(reportForm.mathType) || 0;
    const professional = parseFloat(reportForm.professional) || 0;

    reportForm.totalScore = (
      politics +
      english +
      math +
      professional
    ).toString();
  }

  // 个性化需求和薄弱模块
  reportForm.personalNeeds = studentData.personalNeeds || "";
  reportForm.weakModules = studentData.weakModules || "";

  // 提示用户
  //  ElMessage.success("学员信息已填充到表单中");
};
// 获取学校名称的辅助函数
const getSchoolName = (schoolId) => {
  const school = collegeOptions.value.find((item) => item.value === schoolId);
  return school ? school.label : schoolId;
};

// 获取专业名称的辅助函数
const getMajorName = (majorId) => {
  const major = majorOptions.value.find((item) => item.value === majorId);
  return major ? major.label : majorId;
};

// 重新创建预览中的图表
const recreateChartsInPreview = async () => {
  console.log("开始重新创建预览中的图表");

  try {
    // 获取原始图表容器
    const originalChartContainers = document.querySelectorAll(
      ".content-container [_echarts_instance_]"
    );
    console.log("找到原始图表数量:", originalChartContainers.length);

    if (originalChartContainers.length === 0) {
      console.log("没有找到原始图表");
      return;
    }

    // 等待预览容器完全渲染
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 查找预览容器中对应的图表容器
    const previewChartContainers = previewContainer.value?.querySelectorAll(
      '.preview-chart, [id*="preview-chart"], .echarts-box'
    );
    console.log(
      "预览中找到的图表容器数量:",
      previewChartContainers?.length || 0
    );

    if (!previewChartContainers || previewChartContainers.length === 0) {
      console.log("预览中没有找到图表容器，尝试查找其他选择器");

      // 尝试其他选择器
      const alternativeContainers = previewContainer.value?.querySelectorAll(
        '[id*="echarts"], div[style*="height"]'
      );
      console.log(
        "备用选择器找到的容器数量:",
        alternativeContainers?.length || 0
      );

      if (!alternativeContainers || alternativeContainers.length === 0) {
        console.log("完全没有找到图表容器");
        return;
      }
    }

    // 动态导入echarts
    const echarts = await import("echarts");

    // 为每个预览图表容器重新创建图表
    for (
      let i = 0;
      i <
      Math.min(originalChartContainers.length, previewChartContainers.length);
      i++
    ) {
      const originalChart = originalChartContainers[i];
      const previewContainer = previewChartContainers[i];

      if (originalChart._echarts_instance_ && previewContainer) {
        console.log(`重新创建第${i + 1}个图表`);

        // 设置预览容器样式
        previewContainer.style.width = "100%";
        previewContainer.style.height = "400px";
        previewContainer.style.display = "block";
        previewContainer.style.visibility = "visible";

        // 获取原始图表的配置
        const originalOption = originalChart._echarts_instance_.getOption();

        // 在预览容器中创建新的图表实例
        const newChart = echarts.init(previewContainer);
        newChart.setOption(originalOption);

        // 保存图表实例引用
        previewContainer._echarts_instance_ = newChart;

        console.log(`第${i + 1}个图表重新创建完成`);

        // 等待一下再处理下一个
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }

    console.log("所有图表重新创建完成");
  } catch (error) {
    console.error("重新创建图表失败:", error);
  }
};
// 导出报告功能
const exportReport = async () => {
  if (!reportUrlId.value && pdfStore.pdfUrl.value) {
    window.open(pdfStore.pdfUrl.value, "_blank");
  } else {
    ElMessage.warning("请先预览报告生成PDF文件");
  }
};

const previewPdfRef = ref(null);
// 预览报告功能
const previewReport = async () => {
  //generateReportId.value = 651;
  // console.log(reportUrlId.value);

  // router.push({
  //   name: "previewpdf",
  //   params: { report_id: reportUrlId.value, firstLevelcode: reportForm.firstLevelDiscipline },
  // });

  // /**
  //  * 2024-09-19
  //  * 临时方案，直接跳转到预览页面
  //  */
  // window.open(
  //   `/previewpdf/${reportUrlId.value}/${reportForm.firstLevelDiscipline}`,
  //   "_blank"
  // );

  // return;
  // 检查是否有生成的报告ID
  if (!reportUrlId.value) {
    ElMessage.warning("请先生成报告内容");
    return;
  }

  previewPdfDialogVisible.value = true;

  // 等待DOM更新和组件挂载
  await nextTick();

  // 等待一小段时间确保组件完全挂载
  await new Promise((resolve) => setTimeout(resolve, 100));

  try {
    // 使用实际生成的报告ID加载数据
    await previewPdfRef.value.initPdfData(
      reportUrlId.value,
      reportForm.firstLevelDiscipline
    );
    console.log("PDF数据初始化成功，报告ID:", reportUrlId.value);
  } catch (error) {
    console.error("PDF数据初始化失败:", error);
    ElMessage.error("加载报告数据失败: " + (error.message || "未知错误"));
    previewPdfDialogVisible.value = false;
  }
};
</script>

<style scoped lang="less">
.container {
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
  position: relative;
  padding-top: 72px;
}
.generate-container {
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
  min-height: 100vh;
}

.report-container {
  padding: 20px;
  background-color: #fff;
  margin: 20px;
  border-radius: 4px;
  height: calc(100vh - 67px - 92px);
  overflow: scroll;
}

.credits-info {
  width: 100%;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 150px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

.credits-text {
  font-weight: bold;
  color: #ff9900;
}

.credits-count {
  font-weight: bold;
  color: #ff9900;
  font-size: 18px;
  margin: 0 5px;
}

.credits-unit {
  color: #666;
}

.button-group {
  width: 600px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.action-btn {
  width: 99px;
  height: 36px;
  margin-right: 15px;
  background: #1bb394;
  box-shadow: 2px 7px 8px 1px rgba(0, 0, 0, 0.16);
  border-radius: 10px 10px 10px 10px;
  color: #fff;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
}

/* 步骤样式 */
.steps {
  padding: 0 160px;
  margin-top: 22px;
}

.step-section {
  margin-bottom: 10px;
}

.step-header {
  margin-bottom: 15px;
}

.step-title {
  width: 320px;
  height: 40px;
  padding: 0 15px;
  padding-top: 14px;
  color: #fff;
  border-radius: 5px;
  font-weight: bold;
  background-image: url("@/assets/images/step-bg.png");
  background-repeat: no-repeat;
  text-align: center;
}

.step-content {
  padding: 10px 20px;
  border-radius: 5px;
  position: relative;
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
}

.step-num-tag {
  background-image: url("@/assets/images/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.wide-item {
  grid-column: span 2;
}

.item-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

.full-width {
  width: 100%;
}

.form-grid,
.english-grid,
.school-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper) {
    border-color: #1bb394 !important;

    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper.is-focus) {
    border-color: #1bb394 !important;
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  /* 下拉菜单样式 */
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }

  /* 多选框样式 */
  :deep(.el-select .el-tag) {
    background-color: #e6f7f1;
    border-color: #1bb394;
    color: #1bb394;
  }

  /* select下拉箭头颜色 */
  :deep(.el-select .el-input__suffix) {
    color: #1bb394;
  }
}

.score-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #fff inset;
  }
}

/* 成绩表格 */
.score-grid,
.english-grid {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.score-row,
.english-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.score-item,
.english-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.score-item {
  border: #1bb394 1px solid;
  border-radius: 10px;
  padding-left: 10px;
  position: relative;
  cursor: move;
  /* 显示可拖动光标 */
  transition: all 0.2s ease;

  &.drag-over {
    border: 2px dashed #1bb394;
    background-color: rgba(27, 179, 148, 0.05);
    transform: scale(1.01);
  }

  .score-label {
    height: 100%;
    width: 155px;
    border-right: #666 1px solid;
    box-shadow: 2px 0 2px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-close {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.score-label,
.english-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
}

.english-label {
  text-align: center;
}

.score-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  width: 100%;
}

.score-divider {
  margin: 0 5px;
  color: #999;
}

/* 学校选择 */
.school-grid {
  margin-left: 20px;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 表格样式 */
.score-table {
  width: 100%;
  border: 1px solid #1bb394;
  margin-top: 20px;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #1bb394;
}

.th-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 14px;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 40px;

  &:last-child {
    border-right: none;
  }

  .sel-no-border {
    :deep(.el-select__wrapper) {
      border-color: #fff !important;
      box-shadow: none;
    }

    :deep(.el-select__selected-item) {
      text-align: center;
    }
  }

  .center-select {
    width: 100%;

    :deep(.el-input__inner) {
      text-align: center !important;
    }

    :deep(.el-input__suffix) {
      right: 5px;
    }
  }

  :deep(.center-select-dropdown) {
    .el-select-dropdown__item {
      text-align: center !important;
    }
  }
}

.table-row-score {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
}

.td-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 40px;

  &:last-child {
    border-right: none;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-input__inner) {
    text-align: center;
    box-shadow: none;
    font-weight: bold;
  }
}

.td-cell .el-input {
  width: 100%;
}

.table-input {
  width: 100%;
}

/* 个性化需求和薄弱模块 */
.personal-demands,
.expertise-advice {
  margin-left: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demands-label,
.advice-label {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  width: 100px;
  height: 30px;
  line-height: 30px;
}

.demands-input,
.advice-input {
  flex: 1;
}

/* 表单样式覆盖 */
:deep(.el-textarea__inner) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

/* 按钮样式 */
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.action-button {
  width: 104px;
  height: 38px;
  background: #1bb394;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1bb394;
}

/* 英语栏目样式 */
.english-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.english-label {
  min-width: 100px;
  margin-right: 15px;
}

.no-border-select .el-input {
  border: none;
  box-shadow: none;
}

.no-border-select {
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }
}

/* AI图标旋转动画样式 */
.ai-animation-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 40px;
}

.ai-icon-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  cursor: pointer;
  perspective: 1000px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.ai-icon-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.5s ease, background-image 0.3s ease;
  border-radius: 50%;
}

.ai-icon-layer.outer {
  background-image: url("@/assets/images/ai-bg-1.png");
  z-index: 1;
  width: 200px;
  height: 200px;
  top: 0;
  left: 0;

  &.hover-effect {
    background-image: url("@/assets/images/ai-bg-1_hover.png");
  }
}

.ai-icon-layer.middle {
  background-image: url("@/assets/images/ai-bg-2.png");
  z-index: 2;
  width: 160px;
  height: 160px;
  top: 20px;
  left: 20px;
}

.ai-icon-layer.inner {
  background-image: url("@/assets/images/ai-bg-3.png");
  z-index: 3;
  width: 120px;
  height: 120px;
  top: 40px;
  left: 40px;
}

/* 遮罩层样式 */
.ai-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.ai-overlay-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-large-icon-wrapper {
  position: relative;
  width: 400px;
  height: 400px;
  perspective: 1000px;
}

.ai-large-icon-wrapper .ai-icon-layer.outer {
  width: 400px;
  height: 400px;
}

.ai-large-icon-wrapper .ai-icon-layer.middle {
  width: 320px;
  height: 320px;
  top: 40px;
  left: 40px;
}

.ai-large-icon-wrapper .ai-icon-layer.inner {
  width: 240px;
  height: 240px;
  top: 80px;
  left: 80px;
}

/* 动画效果 */
.animate-outer {
  animation: rotateOuter 5s ease-in-out infinite;
}

.animate-middle {
  animation: rotateMiddle 5s ease-in-out infinite;
}

.animate-inner {
  animation: pulseInner 5s ease-in-out infinite;
}

@keyframes rotateOuter {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(720deg);
  }
}

@keyframes rotateMiddle {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-360deg);
  }

  100% {
    transform: rotate(-720deg);
  }
}

@keyframes pulseInner {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

/* 粒子背景效果 */
.ai-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at 50% 50%,
    rgba(27, 179, 148, 0.2) 0%,
    rgba(0, 0, 0, 0) 60%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }

  100% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

/* 添加光芒效果 */
.ai-large-icon-wrapper::after {
  content: "";
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background: radial-gradient(
    circle at center,
    rgba(27, 179, 148, 0.3) 0%,
    transparent 70%
  );
  z-index: -1;
  animation: glow 4s ease-in-out infinite;
}

@keyframes glow {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 0.3;
  }
}

.echarts-box {
  width: 98%;
  height: 180px;
  margin: 0 auto 18px auto;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
}

/* 院校总览表格样式 */
.school-table-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.school-table-header {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  height: 46px;
  line-height: 46px;
  text-align: center;
  padding: 0 10px;
}

.header-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.school-table-body {
  background-color: #fff;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  border-bottom: 1px solid #f0f0f0;
  height: 60px;
  line-height: 60px;
  text-align: center;
  padding: 0 10px;

  &:hover {
    background-color: #f5f7fa;
  }

  &:nth-child(even) {
    background-color: #f9f9f9;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.body-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
}

.school-name {
  text-align: left;
  font-weight: bold;
  position: relative;
}

.school-tags {
  position: absolute;
  top: 40px;
  left: 10px;
  display: flex;
  gap: 4px;
}

.tag {
  display: inline-block;
  padding: 1px 6px;
  line-height: 1.5;
  font-size: 12px;
  border-radius: 3px;
  color: white;
  font-weight: normal;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

.tag-double {
  background-color: #1bb394;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  margin: 20px 0;
  overflow: hidden;
}

.school-header {
  display: flex;
  padding: 20px;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 14px;
    color: #666;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    margin-right: 0;
  }

  .major-diff {
    font-size: 14px;
    color: #333;
    margin-left: 5px;
  }
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #2fc293;
}

.section-title {
  font-size: 18px;
  margin: 15px 0;
  font-weight: 600;
  position: relative;
  padding-left: 15px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   left: 0;
  //   top: 50%;
  //   transform: translateY(-50%);
  //   width: 4px;
  //   height: 18px;
  //   background-color: #1bb394;
  //   border-radius: 2px;
  // }
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 标签样式增强 */
.tag {
  padding: 2px 8px;
  font-size: 12px;
}

.tag-double {
  background-color: #1bb394;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}
</style>
<style>
.el-message-box__btns .el-button--primary {
  background-color: #1bb394 !important;
  /* 绿色背景 */
  border: none !important;
}

.el-select-dropdown__item.is-selected {
  color: #1bb394 !important;
  font-weight: bold;
}
</style>

<style scoped>
/* 招生情况样式 */
.admission-section {
  margin-top: 30px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

.reexam-container {
  border: 1px solid #e8f5f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e8f5f0;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-icon {
  width: 6px;
  height: 6px;
  background-color: #1bb394;
  border-radius: 50%;
  margin-right: 10px;
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px solid #e8f5f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}

/* 预览弹窗样式 */
.preview-dialog {
  :deep(.el-dialog) {
    max-width: 95vw;
    margin: 0 auto;
  }

  :deep(.el-dialog__body) {
    padding: 10px 20px;
    max-height: 80vh;
    overflow-y: auto;
  }
}

.preview-container {
  width: 100%;
  min-height: 400px;
  position: relative;
}

.preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 16px;
  color: #666;
}

.preview-content {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 确保预览内容中的图表正确显示 */
  :deep([_echarts_instance_]) {
    width: 100% !important;
    height: 400px !important;
  }

  /* 预览内容的基本样式 */
  :deep(.content-container) {
    padding: 0;
  }

  :deep(.step-title) {
    background-color: #1bb394;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
  }

  :deep(.step-content) {
    margin-bottom: 20px;
  }

  /* 学生基本信息样式 */
  :deep(.student-basic-info) {
    margin-bottom: 30px;

    h2 {
      color: #1bb394;
      text-align: center;
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: bold;
    }

    div {
      margin-bottom: 8px;
      line-height: 1.6;

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }

  /* 完整报告预览容器 */
  :deep(.full-report-preview) {
    width: 100%;

    .student-basic-info {
      border-bottom: 2px solid #1bb394;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }

    .report-content {
      .content-container {
        padding: 0;
      }
    }
  }
}

.preview-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #999;
  font-size: 16px;
}
</style>
