<template>
  <div class="container">
    <div class="credits-info">
      <div>
        <span class="credits-text">已生成报告数：</span>
        <span class="credits-count">{{
          userInfo.generated_report_times || 0
        }}</span>
        <span class="credits-count">人次</span>
      </div>

      <div
        class="button-group"
        :style="{
          width: generateReportId && !showCreateBtn ? '900px' : '600px',
        }"
      >
        <div class="action-btn" @click="previewReport">预览报告</div>
        <div class="action-btn" @click="exportReport">导出报告</div>
        <div class="action-btn">发送</div>
        <!-- 重新生成按钮，只有在报告生成后才显示 -->
        <div
          v-if="generateReportId && !showCreateBtn"
          class="action-btn regenerate-btn"
          @click="regenerateRecommendSchools"
        >
          重新生成推荐院校
        </div>
        <div
          v-if="generateReportId && !showCreateBtn"
          class="action-btn regenerate-btn"
          @click="regenerateStudyPlan"
        >
          重新生成学习规划
        </div>
      </div>
    </div>

    <div class="generate-container">
      <div class="report-container">
        <div class="steps">
          <!-- 第一步：个人基础信息 -->
          <div class="step-section">
            <div class="step-header">
              <div class="step-title">第一部分：个人基础信息</div>
            </div>

            <div class="step-content">
              <div class="step-num-tag">
                <span>01</span>
                <div class="tag-text">个人基础信息</div>
              </div>

              <div class="form-grid">
                <div class="form-item">
                  <div class="item-label">学员姓名</div>
                  <el-input
                    v-model="reportForm.name"
                    placeholder="请输入学员姓名"
                  ></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">性别</div>
                  <el-select
                    v-model="reportForm.sex"
                    placeholder="请选择性别"
                    class="full-width"
                  >
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="2"></el-option>
                    <el-option label="其他" value="3"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">本科院校</div>
                  <el-select
                    v-model="reportForm.undergraduateSchool"
                    placeholder="输入关键字搜索院校"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchCollege"
                    :loading="collegeSearchLoading"
                    class="full-width"
                    @change="handleCollegeChange"
                  >
                    <el-option
                      v-for="item in collegeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">本科专业</div>
                  <el-select
                    v-model="reportForm.undergraduateMajor"
                    placeholder="输入关键字搜索专业"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="remoteSearchMajor"
                    :loading="majorSearchLoading"
                    class="full-width"
                    :disabled="!reportForm.undergraduateSchool"
                    @focus="handleMajorFocus"
                    popper-class="major-select-dropdown"
                    @visible-change="handleMajorSelectVisibleChange"
                  >
                    <el-option
                      v-for="item in majorOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                    <div
                      v-if="majorHasMore && !majorSearchLoading"
                      class="load-more-option"
                      @click="loadMoreMajors"
                    >
                      <el-button
                        style="color: #1bb394; padding-left: 20px"
                        type="text"
                        size="small"
                        >点击加载更多...</el-button
                      >
                    </div>
                    <div
                      v-if="majorSearchLoading && majorCurrentPage > 1"
                      class="loading-option"
                    >
                      <el-icon>
                        <Loading />
                      </el-icon>
                      加载中...
                    </div>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">学科门类</div>
                  <el-select
                    v-model="reportForm.disciplineCategory"
                    placeholder="请选择学科门类"
                    class="full-width"
                    @change="handleDisciplineCategoryChange"
                  >
                    <el-option
                      v-for="item in disciplineCategoryOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">一级学科</div>
                  <el-select
                    v-model="reportForm.firstLevelDiscipline"
                    placeholder="请选择一级学科"
                    class="full-width"
                    :disabled="!reportForm.disciplineCategory"
                    @change="handleFirstLevelDisciplineChange"
                  >
                    <el-option
                      v-for="item in firstLevelDisciplineOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">目标专业</div>
                  <el-select
                    v-model="reportForm.targetMajor"
                    placeholder="请选择目标专业"
                    class="full-width"
                    :disabled="!reportForm.firstLevelDiscipline"
                    @change="handleTargetMajorChange"
                    multiple
                  >
                    <el-option
                      v-for="item in secondLevelDisciplineOptions"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">专业代码</div>
                  <el-input
                    v-model="reportForm.majorCode"
                    placeholder="请输入专业代码"
                  ></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">联系方式</div>
                  <el-input
                    v-model="reportForm.phone"
                    placeholder="请输入联系方式"
                  ></el-input>
                </div>

                <div class="form-item">
                  <div class="item-label">考研年份</div>
                  <el-select
                    v-model="reportForm.examYear"
                    placeholder="请选择考研年份"
                    class="full-width"
                  >
                    <el-option label="2027" value="2027"></el-option>
                    <el-option label="2026" value="2026"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">跨专业</div>
                  <el-select
                    v-model="reportForm.isMultiDisciplinary"
                    placeholder="请选择跨专业"
                    class="full-width"
                  >
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="2"></el-option>
                  </el-select>
                </div>

                <div class="form-item">
                  <div class="item-label">培养方式</div>
                  <el-select
                    v-model="reportForm.educationalStyle"
                    placeholder="请选择培养方式"
                    class="full-width"
                  >
                    <el-option label="全日制" value="0"></el-option>
                    <el-option label="非全日制" value="1"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二步：本科成绩情况 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-container">
                <div class="step-num-tag">
                  <span>02</span>
                  <div class="tag-text">本科成绩情况</div>
                </div>
                <div class="create-btn">
                  <el-button
                    type="primary"
                    class="action-button"
                    @click="openAddScoreDialog"
                    >创建</el-button
                  >
                </div>
              </div>

              <div class="score-grid">
                <div class="score-row">
                  <div
                    class="score-item"
                    v-for="item in scoreInfo"
                    :key="item.id"
                    :draggable="true"
                    @dragstart="handleDragStart($event, item)"
                    @dragover.prevent="handleDragOver($event)"
                    @drop="handleDrop($event, item)"
                    @dragend="handleDragEnd"
                  >
                    <div class="score-label">{{ item.title }}</div>
                    <div class="score-input-container">
                      <el-input
                        v-model="item.score"
                        placeholder="140"
                      ></el-input>
                    </div>
                    <el-icon
                      @click="delScoreInfo(item.id)"
                      class="score-close"
                      color="#1BB394"
                      size="14"
                    >
                      <Close />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三步：英语基础 -->
          <div class="step-section">
            <div class="step-content">
              <div class="step-num-tag">
                <span>03</span>
                <div class="tag-text">英语基础</div>
              </div>

              <div class="english-grid">
                <div class="english-row">
                  <div class="english-item">
                    <div class="english-label">高考英语成绩</div>
                    <el-input
                      v-model="reportForm.englishScore"
                      placeholder="请输入高考英语成绩"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">大学四级</div>
                    <el-input
                      v-model="reportForm.cet4"
                      placeholder="请输入大学四级成绩"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">大学六级</div>
                    <el-input
                      v-model="reportForm.cet6"
                      placeholder="请输入大学六级成绩"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">托福</div>
                    <el-input
                      v-model="reportForm.tofelScore"
                      placeholder="请输入托福成绩"
                      class="full-width"
                    ></el-input>
                  </div>
                </div>

                <div class="english-row">
                  <div class="english-item">
                    <div class="english-label">雅思</div>
                    <el-input
                      v-model="reportForm.ieltsScore"
                      placeholder="请输入雅思成绩"
                      class="full-width"
                    ></el-input>
                  </div>

                  <div class="english-item">
                    <div class="english-label">英语能力</div>
                    <el-select
                      v-model="reportForm.englishLevel"
                      placeholder="请选择英语能力"
                      class="full-width"
                    >
                      <el-option label="一般" value="一般"></el-option>
                      <el-option label="良好" value="良好"></el-option>
                      <el-option label="优秀" value="优秀"></el-option>
                    </el-select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第四步：目标院校梯度 -->
        <div class="step-section">
          <div class="step-content">
            <div class="step-num-tag">
              <span>04</span>
              <div class="tag-text">目标院校梯度</div>
            </div>

            <div class="school-grid">
              <div class="form-item">
                <div class="item-label">地区倾向</div>
                <el-select
                  v-model="reportForm.region"
                  placeholder="请选择地区倾向"
                  multiple
                  class="full-width"
                >
                  <el-option label="A区" value="A区"></el-option>
                  <el-option label="B区" value="B区"></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">省份选择</div>
                <el-select
                  v-model="reportForm.intendedSchools"
                  placeholder="请选择省份"
                  multiple
                  class="full-width"
                >
                  <el-option
                    v-for="item in filteredProvinceData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">梦校</div>
                <el-select
                  v-model="reportForm.targetSchool"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="输入关键字搜索院校"
                  :remote-method="remoteSearchDreamSchool"
                  :loading="dreamSchoolSearchLoading"
                  class="full-width"
                  @change="
                    (val) => {
                      if (val) {
                        const selected = dreamSchoolOptions.find(
                          (item) => item.value === val
                        );
                        if (selected) {
                          reportForm.targetSchoolName = selected.label;
                        }
                      } else {
                        reportForm.targetSchoolName = '';
                      }
                    }
                  "
                >
                  <el-option
                    v-for="item in dreamSchoolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

              <div class="form-item">
                <div class="item-label">院校层次</div>
                <el-select
                  v-model="reportForm.schoolLevel"
                  multiple
                  placeholder="请选择院校层次"
                  class="full-width"
                >
                  <el-option label="985" value="985"></el-option>
                  <el-option label="211" value="211"></el-option>
                  <el-option label="双一流" value="双一流"></el-option>
                  <el-option label="双非" value="双非"></el-option>
                </el-select>
              </div>

              <div class="form-item wide-item">
                <div class="item-label">专业课指定参考书</div>
                <el-input
                  v-model="reportForm.referenceBooks"
                  placeholder="请输入专业课指定参考书"
                ></el-input>
              </div>
            </div>
          </div>
        </div>

        <!-- 第五步：考研成绩预估 -->
        <div class="step-section">
          <div class="step-content">
            <div class="step-num-tag">
              <span>05</span>
              <div class="tag-text">考研成绩预估</div>
            </div>

            <div class="score-table">
              <div class="table-header">
                <div class="th-cell cell-color">政治</div>
                <div class="th-cell cell-color">英语</div>
                <div class="th-cell cell-color">业务课一</div>
                <div class="th-cell cell-color">业务课二</div>
                <div class="th-cell cell-color">总分</div>
              </div>
              <div class="table-row-score">
                <div class="td-cell">
                  <el-input
                    v-model="reportForm.politics"
                    class="table-input"
                    @input="calculateTotalScore"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="reportForm.englishS"
                    class="table-input"
                    @input="calculateTotalScore"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="reportForm.englishType"
                    class="table-input"
                    @input="calculateTotalScore"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="reportForm.mathType"
                    class="table-input"
                    @input="calculateTotalScore"
                  ></el-input>
                </div>
                <div class="td-cell">
                  <el-input
                    v-model="reportForm.totalScore"
                    class="table-input"
                    readonly
                  ></el-input>
                </div>
              </div>
            </div>

            <div class="personal-demands">
              <div class="demands-label">个性化需求</div>
              <el-input
                v-model="reportForm.personalNeeds"
                type="textarea"
                :rows="1"
                class="demands-input"
              ></el-input>
            </div>

            <div class="expertise-advice">
              <div class="advice-label">薄弱模块</div>
              <el-input
                v-model="reportForm.weakModules"
                type="textarea"
                :rows="1"
                class="advice-input"
              ></el-input>
            </div>
          </div>
        </div>

        <!-- AI图标旋转动画 -->
        <div class="ai-animation-container" v-if="showCreateBtn">
          <div
            class="ai-icon-wrapper"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
            @click="toggleAIOverlay"
            v-show="!showContent"
          >
            <div
              class="ai-icon-layer outer"
              :class="{
                'animate-outer': isAnimating,
                'hover-effect': isHovering,
              }"
            ></div>
            <div
              class="ai-icon-layer middle"
              :class="{ 'animate-middle': isAnimating }"
            ></div>
            <div
              class="ai-icon-layer inner"
              :class="{ 'animate-inner': isAnimating }"
            ></div>
          </div>
        </div>

        <!-- AI遮罩层 -->
        <div class="ai-overlay" v-if="showAIOverlay">
          <div class="ai-overlay-content">
            <div class="ai-large-icon-wrapper">
              <div
                class="ai-icon-layer outer"
                :class="{ 'animate-outer': true }"
              ></div>
              <div
                class="ai-icon-layer middle"
                :class="{ 'animate-middle': true }"
              ></div>
              <div
                class="ai-icon-layer inner"
                :class="{ 'animate-inner': true }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 专业分析组件 -->
      <MajorAnalysis
        ref="majorAnalysisRef"
        :national-line-data="nationalLineData"
        :visible="showMajorAnalysis"
      />

      <report-content ref="reportContentElement"></report-content>
      <WeakModuleAnalysis ref="weakModuleAnalysisRef"></WeakModuleAnalysis>
    </div>
  </div>
  <!-- 预览弹窗 -->
  <el-dialog
    v-model="previewDialogVisible"
    title="报告预览"
    width="90%"
    top="5vh"
    :close-on-click-modal="false"
    class="preview-dialog"
  >
    <div class="preview-container" ref="previewContainer">
      <div
        v-if="previewLoading"
        class="preview-loading"
        v-loading="previewLoading"
        element-loading-text="正在生成预览..."
      ></div>
      <div
        v-else-if="previewContent"
        v-html="previewContent"
        class="preview-content"
      ></div>
      <div v-else class="preview-empty">
        <p>暂无预览内容</p>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="generatePDFFromPreview"
          :loading="pdfGenerating"
        >
          {{ pdfGenerating ? "生成中..." : "生成PDF" }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加成绩对话框 -->
  <el-dialog
    title="添加成绩项"
    v-model="dialogVisible"
    width="30%"
    :close-on-click-modal="false"
  >
    <el-form :model="newScoreForm" label-width="80px">
      <el-form-item label="科目名称">
        <el-input
          v-model="newScoreForm.title"
          placeholder="请输入科目名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="成绩">
        <el-input
          v-model="newScoreForm.score"
          placeholder="请输入成绩"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          style="background-color: #1bb394"
          @click="addNewScore"
          >确认</el-button
        >
      </span>
    </template>
  </el-dialog>

  <el-dialog
    title="预览报告"
    v-model="previewPdfDialogVisible"
    width="860px"
    :close-on-click-modal="false"
  >
    <preview-pdf ref="previewPdfRef"></preview-pdf>
  </el-dialog>
</template>

<script setup>
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  nextTick,
  watch,
  computed,
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { usePdfStore } from "@/store/modules/pdf";
import {
  searchSchool,
  searchMajor,
  searchTargetMajor,
  getNationalLineData,
} from "@/api/school";
import {
  toggleAIOverlay as toggleAIOverlayApi,
  getDisciplineCategories,
  getFirstLevelDisciplines,
  getSecondLevelDisciplines,
} from "@/api/student";
import provinceData from "@/utils/province.json";
import * as echarts from "echarts";
import reportContent from "./components/content.vue";
import MajorAnalysis from "./components/MajorAnalysis.vue";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { upload } from "@/utils/upload";
import { updatePdfUrl } from "@/api/report";
import PreviewPdf from "./components/PreviewPdf.vue";
import WeakModuleAnalysis from "./components/WeakModuleAnalysis.vue";

//学习计划组件
const weakModuleAnalysisRef = ref(null);
const previewDialogVisible = ref(false);
const reportContentElement = ref(null);
const majorAnalysisRef = ref(null);
let showCreateBtn = ref(true);
// 加载状态
const loading = ref(false);
const collegeSearchLoading = ref(false);
const collegeOptions = ref([]);
const collegeSearchTimeout = ref(null);
const collegeSearchCache = {};

const majorSearchLoading = ref(false);
const majorOptions = ref([]);
const majorSearchTimeout = ref(null);
const majorSearchCache = {};

const targetMajorSearchLoading = ref(false);
const targetMajorOptions = ref([]);
const targetMajorSearchTimeout = ref(null);
const targetMajorSearchCache = {};

// 学科分类相关数据
const disciplineCategoryOptions = ref([]);
const firstLevelDisciplineOptions = ref([]);
const secondLevelDisciplineOptions = ref([]);

// 梦校搜索相关
const dreamSchoolSearchLoading = ref(false);
const dreamSchoolOptions = ref([]);
const dreamSchoolSearchTimeout = ref(null);
const dreamSchoolSearchCache = {};

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
const pdfStore = usePdfStore();
//清空之前的记录
pdfStore.clearPdfUrl();
// 表单数据
const reportForm = reactive({
  student_id: 0,
  // 第一步：个人基础信息
  name: "",
  sex: "", // 1-男, 2-女
  undergraduateSchool: "",
  undergraduateSchoolName: "",
  undergraduateMajor: "",
  undergraduateMajorName: "",
  disciplineCategory: null,
  firstLevelDiscipline: null,
  targetMajor: "",
  targetMajorName: "",
  majorCode: "",
  phone: "",
  examYear: "",
  isMultiDisciplinary: "", // 1-是, 2-否
  educationalStyle: "", //培养方式

  // 第二步：本科成绩情况
  gaokaoScore: "",
  mathScore1: "",
  mathScore2: "",
  statScore: "",
  linearScore: "",
  majorScore1: "",
  majorScore2: "",
  majorScore3: "",

  // 第三步：英语基础
  englishScore: "",
  cet4: "",
  cet6: "",
  tofelScore: "",
  ieltsScore: "",
  englishLevel: "",

  // 第四步：目标院校梯度
  region: [], // 保持数组，支持多选，默认包含所有区域
  intendedSchools: [], // 初始为空数组，会根据选择的区域动态填充
  targetSchool: "",
  targetSchoolName: "", // 梦校名称
  schoolLevel: "",
  referenceBooks: "",

  // 第五步：考研成绩预估
  politics: "",
  englishType: "",
  mathType: "",
  professional: "",
  totalScore: "",
  personalNeeds: "",
  weakModules: "",
});

let scoreInfo = ref([]);
let showContent = ref(false);
let pdfUrl = ref("");
let generateReportId = ref("");

// 预览功能相关
const previewPdfDialogVisible = ref(false);
const previewLoading = ref(false);
const previewContent = ref("");
const previewContainer = ref(null);

// 添加PDF生成loading状态
const pdfGenerating = ref(false);

// 添加成绩相关
const dialogVisible = ref(false);
const newScoreForm = reactive({
  title: "",
  score: "",
});

// 拖拽相关状态
const draggedItem = ref(null);

// 拖拽开始
const handleDragStart = (event, item) => {
  draggedItem.value = item;
  event.dataTransfer.effectAllowed = "move";
  // 设置拖拽时的半透明效果
  event.target.style.opacity = "0.5";
};

// 拖拽经过
const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = "move";

  // 添加拖拽经过的视觉反馈
  const targetElement = event.currentTarget;
  if (
    targetElement &&
    draggedItem.value &&
    targetElement !== draggedItem.value
  ) {
    targetElement.classList.add("drag-over");
  }
};

// 拖拽放置
const handleDrop = (event, targetItem) => {
  event.preventDefault();

  // 移除所有元素的拖拽效果类
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });

  if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
    return;
  }

  // 找到拖拽项和目标项的索引
  const dragIndex = scoreInfo.value.findIndex(
    (item) => item.id === draggedItem.value.id
  );
  const targetIndex = scoreInfo.value.findIndex(
    (item) => item.id === targetItem.id
  );

  if (dragIndex < 0 || targetIndex < 0) {
    return;
  }

  // 重新排序
  const newScoreInfo = [...scoreInfo.value];
  const [removed] = newScoreInfo.splice(dragIndex, 1);
  newScoreInfo.splice(targetIndex, 0, removed);

  // 更新成绩列表
  scoreInfo.value = newScoreInfo;

  // 清除拖拽状态
  draggedItem.value = null;

  // 恢复透明度
  document.querySelectorAll(".score-item").forEach((el) => {
    el.style.opacity = "1";
  });

  ElMessage.success("排序已更新");
};

// 拖拽结束
const handleDragEnd = (event) => {
  // 恢复透明度
  event.target.style.opacity = "1";

  // 可以添加拖拽结束的视觉反馈
  const scoreItems = document.querySelectorAll(".score-item");
  scoreItems.forEach((el) => {
    el.classList.remove("drag-over");
  });
};

// 打开添加成绩对话框
const openAddScoreDialog = () => {
  dialogVisible.value = true;
  // 重置表单
  newScoreForm.title = "";
  newScoreForm.score = "";
};

// 添加新成绩
const addNewScore = () => {
  // 验证表单
  if (!newScoreForm.title || !newScoreForm.score) {
    ElMessage.warning("科目名称和成绩不能为空");
    return;
  }

  // 生成新ID (取当前最大ID + 1)
  const maxId = Math.max(...scoreInfo.value.map((item) => item.id), 0);
  const newId = maxId + 1;

  // 添加到成绩列表
  scoreInfo.value.push({
    id: newId,
    title: newScoreForm.title,
    score: Number(newScoreForm.score) || newScoreForm.score,
  });

  // 关闭对话框
  dialogVisible.value = false;
  ElMessage.success("添加成功");
};

// 搜索本科院校
const remoteSearchCollege = (query) => {
  if (query !== "") {
    collegeSearchLoading.value = true;

    // 检查缓存中是否已有该查询结果
    if (collegeSearchCache[query]) {
      collegeOptions.value = collegeSearchCache[query];
      collegeSearchLoading.value = false;
      return;
    }

    // 延迟300ms，避免频繁请求
    clearTimeout(collegeSearchTimeout.value);
    collegeSearchTimeout.value = setTimeout(() => {
      searchSchool(query)
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 假设后端返回的数据格式是 {code: 0, data: [{id: 1, name: '中国农业大学'}, ...]}
            const options = res.data.map((item) => ({
              value: item.id.toString(),
              label: item.name,
            }));
            collegeOptions.value = options;
            // 添加到缓存
            collegeSearchCache[query] = options;
          } else {
            collegeOptions.value = [];
          }
        })
        .catch(() => {
          collegeOptions.value = [];
        })
        .finally(() => {
          collegeSearchLoading.value = false;
        });
    }, 300);
  } else {
    collegeOptions.value = [];
  }
};

//删除本科成绩
const delScoreInfo = (id) => {
  ElMessageBox.confirm("确定删除该成绩吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    closeOnPressEscape: false,
    showClose: false,
  })
    .then(() => {
      // 删除成绩
      scoreInfo.value = scoreInfo.value.filter((item) => item.id !== id);
      ElMessage.success("删除成功");
    })
    .catch(() => {});
};

// 搜索本科专业
const remoteSearchMajor = (query) => {
  if (!reportForm.undergraduateSchool) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果是新的搜索查询，重置分页状态
  if (query !== lastMajorQuery.value) {
    majorCurrentPage.value = 1;
    majorOptions.value = [];
    majorHasMore.value = false;
    lastMajorQuery.value = query;
  }

  majorSearchLoading.value = true;

  // 生成缓存键，包含学校ID、查询词和页码
  const cacheKey = `${reportForm.undergraduateSchool}_${query || ""}_${
    majorCurrentPage.value
  }`;

  // 检查缓存中是否已有该查询结果
  if (majorSearchCache[cacheKey]) {
    const cachedResult = majorSearchCache[cacheKey];
    if (majorCurrentPage.value === 1) {
      majorOptions.value = cachedResult.data;
    } else {
      majorOptions.value = [...majorOptions.value, ...cachedResult.data];
    }
    majorHasMore.value = cachedResult.hasMore;
    majorSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(majorSearchTimeout.value);
  majorSearchTimeout.value = setTimeout(() => {
    searchMajor(
      reportForm.undergraduateSchool,
      query || "",
      majorCurrentPage.value
    )
      .then((res) => {
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.major_name,
          }));

          // 处理分页数据
          if (majorCurrentPage.value === 1) {
            majorOptions.value = options;
          } else {
            majorOptions.value = [...majorOptions.value, ...options];
          }

          // 更新分页状态
          majorHasMore.value = res.pagination ? res.pagination.has_more : false;

          // 添加到缓存
          majorSearchCache[cacheKey] = {
            data: options,
            hasMore: majorHasMore.value,
          };
        } else {
          if (majorCurrentPage.value === 1) {
            majorOptions.value = [];
          }
          majorHasMore.value = false;
        }
      })
      .catch(() => {
        if (majorCurrentPage.value === 1) {
          majorOptions.value = [];
        }
        majorHasMore.value = false;
      })
      .finally(() => {
        majorSearchLoading.value = false;
        majorIsInitialized.value = true;
      });
  }, 300);
};

// 搜索目标专业
const remoteSearchTargetMajor = (query) => {
  if (query !== "") {
    targetMajorSearchLoading.value = true;

    // 检查缓存中是否已有该查询结果
    if (targetMajorSearchCache[query]) {
      targetMajorOptions.value = targetMajorSearchCache[query];
      targetMajorSearchLoading.value = false;
      return;
    }

    // 延迟300ms，避免频繁请求
    clearTimeout(targetMajorSearchTimeout.value);
    targetMajorSearchTimeout.value = setTimeout(() => {
      searchTargetMajor(query)
        .then((res) => {
          if (res.code === 0 && res.data) {
            const options = res.data.map((item) => ({
              value: item.name,
              label: item.name,
              code: item.code,
            }));
            targetMajorOptions.value = options;
            // 添加到缓存
            targetMajorSearchCache[query] = options;
          } else {
            targetMajorOptions.value = [];
          }
        })
        .catch(() => {
          targetMajorOptions.value = [];
        })
        .finally(() => {
          targetMajorSearchLoading.value = false;
        });
    }, 300);
  } else {
    targetMajorOptions.value = [];
  }
};

// 搜索梦校
const remoteSearchDreamSchool = (query) => {
  if (!query) {
    dreamSchoolOptions.value = [];
    return;
  }

  dreamSchoolSearchLoading.value = true;
  console.log("搜索梦校:", query);

  // 检查缓存中是否已有该查询结果
  if (dreamSchoolSearchCache[query]) {
    dreamSchoolOptions.value = dreamSchoolSearchCache[query];
    dreamSchoolSearchLoading.value = false;
    return;
  }

  // 延迟300ms，避免频繁请求
  clearTimeout(dreamSchoolSearchTimeout.value);
  dreamSchoolSearchTimeout.value = setTimeout(() => {
    searchSchool(query)
      .then((res) => {
        console.log("搜索梦校结果:", res);
        if (res.code === 0 && res.data) {
          const options = res.data.map((item) => ({
            value: item.id.toString(),
            label: item.name,
          }));
          dreamSchoolOptions.value = options;
          // 添加到缓存
          dreamSchoolSearchCache[query] = options;

          // 如果选择了梦校，保存梦校名称
          if (reportForm.targetSchool) {
            const selectedSchool = options.find(
              (item) => item.value === reportForm.targetSchool
            );
            if (selectedSchool) {
              reportForm.targetSchoolName = selectedSchool.label;
            }
          }
        } else {
          dreamSchoolOptions.value = [];
        }
      })
      .catch((err) => {
        console.error("获取梦校列表失败", err);
        dreamSchoolOptions.value = [];
      })
      .finally(() => {
        dreamSchoolSearchLoading.value = false;
      });
  }, 300);
};

// 监听本科院校变更
const handleCollegeChange = () => {
  // 清空专业选择
  reportForm.major = "";
  reportForm.targetMajor = "";
  // 清空专业选项
  majorOptions.value = [];
  targetMajorOptions.value = [];

  // 重置本科专业分页状态
  majorCurrentPage.value = 1;
  majorHasMore.value = false;
  majorIsInitialized.value = false;
  lastMajorQuery.value = "";
};

// 处理学科门类变更
const handleDisciplineCategoryChange = async (value) => {
  console.log("学科门类变更:", value);

  // 清空下级选择
  reportForm.firstLevelDiscipline = null;
  reportForm.targetMajor = "";
  reportForm.targetMajorName = "";
  reportForm.majorCode = "";
  firstLevelDisciplineOptions.value = [];
  secondLevelDisciplineOptions.value = [];

  if (value) {
    try {
      const res = await getFirstLevelDisciplines(value);
      if (res.code === 0) {
        firstLevelDisciplineOptions.value = res.data;
      } else {
        ElMessage.error(res.msg || "获取一级学科失败");
      }
    } catch (error) {
      console.error("获取一级学科失败", error);
      ElMessage.error("获取一级学科失败，请稍后重试");
    }
  }
};

// 处理一级学科变更
const handleFirstLevelDisciplineChange = async (value) => {
  console.log("一级学科变更:", value);

  // 清空下级选择
  reportForm.targetMajor = "";
  reportForm.targetMajorName = "";
  reportForm.majorCode = "";
  secondLevelDisciplineOptions.value = [];

  if (value) {
    try {
      const res = await getSecondLevelDisciplines(value);
      if (res.code === 0) {
        secondLevelDisciplineOptions.value = res.data;

        // 如果只有一级学科，设置专业代码为一级学科代码加*
        const selectedFirstLevel = firstLevelDisciplineOptions.value.find(
          (item) => item.id === value
        );
        if (selectedFirstLevel && selectedFirstLevel.value) {
          reportForm.majorCode = selectedFirstLevel.value + "*";
          reportForm.targetMajorName = selectedFirstLevel.label;
        }
      } else {
        ElMessage.error(res.msg || "获取目标专业失败");
      }
    } catch (error) {
      console.error("获取目标专业失败", error);
      ElMessage.error("获取目标专业失败，请稍后重试");
    }
  }
};

// 监听目标专业变更
const handleTargetMajorChange = (value) => {
  const selectedIds = Array.isArray(value) ? value : [];
  // 根据选择的专业找到对应的专业代码
  const selectedMajors = secondLevelDisciplineOptions.value.filter((item) =>
    selectedIds.includes(item.id)
  );
  if (selectedMajors.length > 0) {
    // 保存专业名称
    reportForm.targetMajorName = selectedMajors.map((item) => item.label);
    reportForm.majorCode = selectedMajors
      .map((item) => item.major_code)
      .join(",");

    reportForm.targetMajor = selectedMajors.map((item) => item.id);

    console.log("设置后的targetMajor:", reportForm.targetMajor);
  } else {
    // 清空专业代码
    reportForm.majorCode = "";
    // 清空专业名称
    reportForm.targetMajorName = [];
    reportForm.targetMajor = [];
  }
};

// 监听区域变更，清空省份选择
watch(
  () => reportForm.region,
  () => {
    // 当区域变化时，直接清空省份选择
    reportForm.intendedSchools = [];
  },
  { deep: true } // 添加 deep 选项，确保数组内容变化时也能触发监听
);

// 提交数据
const handleSubmit = () => {
  ElMessage.success("报告提交成功！");
};

// 已移除原有的下拉选择选项，现在使用固定标题：政治、英语、业务课一、业务课二

// 根据所选区域过滤省份数据
const filteredProvinceData = computed(() => {
  // 始终返回所有省份，不再根据区域过滤
  // 这样可以确保所有省份都可见，无论选择了哪个区域
  return provinceData;
});

// 已移除原有的标签获取方法，现在使用固定标题

// 旋转动画相关
const isAnimating = ref(false);
const isHovering = ref(false);
const showAIOverlay = ref(false);

// 处理鼠标悬停
const handleMouseEnter = () => {
  isHovering.value = true;
};

// 处理鼠标离开
const handleMouseLeave = () => {
  isHovering.value = false;
};

// 根据专业代码获取一级学科
const getFirstLevelSubject = (code) => {
  if (!code || code.length < 2) return "";

  // 根据专业代码前两位确定一级学科
  const subjectCode = code.substring(0, 2);
  const subjectMap = {
    "01": "哲学",
    "02": "经济学",
    "03": "法学",
    "04": "教育学",
    "05": "文学",
    "06": "历史学",
    "07": "理学",
    "08": "工学",
    "09": "农学",
    10: "医学",
    11: "军事学",
    12: "管理学",
    13: "艺术学",
  };

  return subjectMap[subjectCode] || "";
};

// 计算总分 - 字段映射：politics=政治，englishS=英语，englishType=业务课一，mathType=业务课二
const calculateTotalScore = () => {
  const politics = parseFloat(reportForm.politics) || 0;
  const english = parseFloat(reportForm.englishS) || 0; // 英语成绩
  const businessOne = parseFloat(reportForm.englishType) || 0; // 业务课一
  const businessTwo = parseFloat(reportForm.mathType) || 0; // 业务课二

  reportForm.totalScore = (
    politics +
    english +
    businessOne +
    businessTwo
  ).toString();
};

// 切换遮罩层显示状态
const toggleAIOverlay = async () => {
  // weakModuleAnalysisRef.value.fetchStudyPlan(537);
  // return;
  //渲染学习计划
  // 在显示AI内容前验证表单
  if (!showContent.value) {
    // 验证学员基本信息
    const validateBasicInfo = () => {
      const errors = [];

      // 第一步：个人基础信息验证（除了托福、雅思成绩、个性化需求和薄弱模块外）
      if (!reportForm.name) errors.push("学员姓名不能为空");
      if (!reportForm.sex) errors.push("性别不能为空");
      if (!reportForm.undergraduateSchool) errors.push("本科院校不能为空");
      if (!reportForm.undergraduateMajor) errors.push("本科专业不能为空");
      if (!reportForm.targetMajor) errors.push("目标专业不能为空");
      if (!reportForm.majorCode) errors.push("专业代码不能为空");

      if (!reportForm.educationalStyle) errors.push("培养方式不能为空");
      if (!reportForm.phone) errors.push("联系方式不能为空");
      if (!reportForm.examYear) errors.push("考研年份不能为空");
      if (!reportForm.isMultiDisciplinary) errors.push("跨专业选项不能为空");

      // 第三步：英语基础验证（除了托福、雅思成绩外）
      // if (!reportForm.englishScore) errors.push("高考英语成绩不能为空");
      // if (!reportForm.cet4) errors.push("大学四级成绩不能为空");
      // if (!reportForm.cet6) errors.push("大学六级成绩不能为空");
      // if (!reportForm.englishLevel) errors.push("英语能力不能为空");

      // 第四步：目标院校梯度验证
      if (!reportForm.region || reportForm.region.length === 0)
        errors.push("地区倾向不能为空");
      if (
        !reportForm.intendedSchools ||
        reportForm.intendedSchools.length === 0
      )
        errors.push("省份选择不能为空");
      if (!reportForm.targetSchool) errors.push("梦校不能为空");
      if (!reportForm.schoolLevel) errors.push("院校层次不能为空");
      // if (!reportForm.referenceBooks) errors.push("专业课指定参考书不能为空");

      // 第五步：考研成绩预估验证
      // if (!reportForm.politics) errors.push("政治成绩预估不能为空");
      // if (!reportForm.englishType) errors.push("英语成绩预估不能为空");
      // if (!reportForm.mathType) errors.push("数学成绩预估不能为空");
      // if (!reportForm.professional) errors.push("专业课成绩预估不能为空");
      // if (!reportForm.totalScore) errors.push("总分预估不能为空");

      // 检查是否有本科成绩
      if (reportForm.isMultiDisciplinary == 1 && scoreInfo.value.length === 0) {
        errors.push("请添加至少一项本科成绩");
      }
      return errors;
    };

    const errors = validateBasicInfo();
    if (errors.length > 0) {
      // 显示错误信息
      ElMessage.error(errors[0]);
      return; // 阻止继续执行
    }
  }

  // 验证通过，继续执行原有逻辑
  showAIOverlay.value = !showAIOverlay.value;

  if (showAIOverlay.value) {
    // 显示遮罩层时触发动画
    isAnimating.value = true;

    // 创建全局变量，用于传递专业名称、代码和一级学科给content.vue
    window.reportData = {
      targetMajorName: reportForm.targetMajor,
      majorCode: reportForm.majorCode,
      firstLevelSubject: getFirstLevelSubject(reportForm.majorCode), // 根据专业代码获取一级学科
    };

    //本科专业
    let undergraduateMajorName = "";
    if (reportForm.undergraduateMajorName) {
      undergraduateMajorName = reportForm.undergraduateMajorName;
    } else {
      undergraduateMajorName =
        majorOptions.value.find(
          (item) => item.value === reportForm.undergraduateMajor
        )?.label || "";
    }
    //本科专业
    let undergraduateSchoolName = "";
    if (reportForm.undergraduateSchoolName) {
      undergraduateSchoolName = reportForm.undergraduateSchoolName;
    } else {
      undergraduateSchoolName =
        collegeOptions.value.find(
          (item) => item.value === reportForm.undergraduateSchool
        )?.label || "";
    }

    // 准备提交的数据
    const formData = {
      // 基本信息
      name: reportForm.name,
      sex: reportForm.sex,
      phone: reportForm.phone,
      undergraduateSchool: reportForm.undergraduateSchool,
      undergraduateSchoolName: undergraduateSchoolName,
      undergraduateMajor: reportForm.undergraduateMajor,
      undergraduateMajorName: undergraduateMajorName,
      disciplineCategory: reportForm.disciplineCategory,
      firstLevelDiscipline: reportForm.firstLevelDiscipline,
      targetMajor: reportForm.targetMajor,
      targetMajorName: reportForm.targetMajorName,
      majorCode: reportForm.majorCode,
      examYear: reportForm.examYear,
      isMultiDisciplinary: reportForm.isMultiDisciplinary,
      educationalStyle: reportForm.educationalStyle,
      // 本科成绩
      mathScores: scoreInfo.value,
      undergraduateTranscript: scoreInfo.value,

      // 英语基础
      englishScore: reportForm.englishScore,
      cet4: reportForm.cet4 !== undefined ? reportForm.cet4 : "",
      cet6: reportForm.cet6 !== undefined ? reportForm.cet6 : "",
      tofelScore: reportForm.tofelScore, // 使用新的字段名
      ieltsScore: reportForm.ieltsScore,
      englishAbility:
        reportForm.englishLevel !== undefined ? reportForm.englishLevel : "",

      // 目标院校
      // 将region数组转换为字符串，如果是数组则取第一个元素
      targetRegion:
        Array.isArray(reportForm.region) && reportForm.region.length > 0
          ? reportForm.region[0]
          : reportForm.region || "",
      // 保持原有的region字段以兼容旧代码
      region:
        Array.isArray(reportForm.region) && reportForm.region.length > 0
          ? reportForm.region[0]
          : reportForm.region || "",
      // 确保targetProvinces是字符串，如果是数组则转换为字符串
      targetProvinces: Array.isArray(reportForm.intendedSchools)
        ? reportForm.intendedSchools.join(",")
        : reportForm.intendedSchools || "",
      targetSchool: reportForm.targetSchool,
      targetSchoolName: reportForm.targetSchoolName,
      schoolLevel: reportForm.schoolLevel,
      referenceBooks: reportForm.referenceBooks,

      // 考研成绩预估 - 字段映射：politics=政治，englishS=英语，englishType=业务课一，mathType=业务课二
      politics: reportForm.politics,
      englishType: reportForm.englishType, // 业务课一
      englishS: reportForm.englishS, // 英语成绩
      mathType: reportForm.mathType, // 业务课二
      mathScore: reportForm.mathScore1,
      professionalScore: reportForm.professional,
      totalScore: reportForm.totalScore,
      personalNeeds: reportForm.personalNeeds,
      weakModules: reportForm.weakModules,
    };

    if (reportForm.student_id != 0) {
      formData["student_id"] = reportForm.student_id;
    }
    console.log(formData);
    try {
      // 第一步：调用后端API处理学生信息
      console.log("开始处理学生信息...");

      console.log(formData);

      const res = await toggleAIOverlayApi(formData);
      if (res.code !== 0) {
        throw new Error(res.msg || "学生信息处理失败");
      }
      console.log("学生信息处理成功:", res.data);
      const reportId = res.data.report_id;
      generateReportId.value = res.data.report_id;

      //关闭遮罩层并显示内容
      showAIOverlay.value = false;
      reportContentElement.value.show();
      // 如果有报告ID，调用AI推荐接口
      if (reportId) {
        //渲染学习计划
        weakModuleAnalysisRef.value.fetchStudyPlan(reportId);
        await reportContentElement.value.setReportId(reportId);
        showCreateBtn.value = false;

        // 获取国家线数据
        if (reportForm.majorCode) {
          console.log("开始获取国家线数据...", reportForm.firstLevelDiscipline);
          try {
            const nationalLineRes = await getNationalLineData(
              reportForm.firstLevelDiscipline
            );

            if (nationalLineRes.code === 0 && nationalLineRes.data) {
              console.log("获取国家线数据成功:", nationalLineRes.data);
              // 设置国家线数据并显示专业分析组件
              nationalLineData.value = nationalLineRes.data;
              showMajorAnalysis.value = true;

              // 将数据传递给content组件
              if (reportContentElement.value) {
                reportContentElement.value.updateNationalLineData(
                  nationalLineRes.data
                );
              }
            } else {
              console.error("获取国家线数据失败:", nationalLineRes.msg);
              ElMessage.warning(
                "获取国家线数据失败: " + (nationalLineRes.msg || "未知错误")
              );
            }
          } catch (nationalLineError) {
            console.error("获取国家线数据异常:", nationalLineError);
            ElMessage.error("获取国家线数据异常，将使用默认数据");
          }
        } else {
          console.warn("缺少专业代码，无法获取国家线数据");
          ElMessage.warning("缺少专业代码，无法获取国家线数据");
        }
      }
    } catch (error) {
      console.error("处理过程中发生错误:", error);
      //ElMessage.error("处理失败: " + (error.message || "未知错误"));

      // 即使出错也关闭遮罩层并显示内容
      showAIOverlay.value = false;
      //reportContentElement.value.show();
    }
  }
};

// 重新生成推荐院校
const regenerateRecommendSchools = async () => {
  if (!generateReportId.value) {
    ElMessage.error("报告ID不存在，无法重新生成");
    return;
  }

  try {
    ElMessage.info("正在重新生成推荐院校...");

    // 清空之前生成的数据
    if (reportContentElement.value) {
      reportContentElement.value.resetData();
    }

    // 重新设置报告ID并生成推荐院校
    await reportContentElement.value.setReportId(generateReportId.value);

    ElMessage.success("推荐院校重新生成完成");
  } catch (error) {
    console.error("重新生成推荐院校失败:", error);
    ElMessage.error("重新生成推荐院校失败，请稍后重试");
  }
};

// 重新生成学习规划
const regenerateStudyPlan = async () => {
  if (!generateReportId.value) {
    ElMessage.error("报告ID不存在，无法重新生成");
    return;
  }

  try {
    ElMessage.info("正在重新生成学习规划...");

    // 清空之前生成的学习规划数据
    if (weakModuleAnalysisRef.value) {
      // 重新获取学习规划
      weakModuleAnalysisRef.value.fetchStudyPlan(generateReportId.value);
    }

    ElMessage.success("学习规划重新生成完成");
  } catch (error) {
    console.error("重新生成学习规划失败:", error);
    ElMessage.error("重新生成学习规划失败，请稍后重试");
  }
};

// 处理窗口大小调整
const handleResize = () => {
  if (chartA) {
    chartA.resize();
  }
  if (chartB) {
    chartB.resize();
  }
};

onMounted(() => {
  nextTick(() => {
    window.addEventListener("resize", handleResize);

    // 添加自定义事件监听器，用于接收学员选择事件
    window.addEventListener("student-selected", handleStudentSelected);

    // 页面加载时检查是否有预选的学员数据
    // const selectedStudentData = localStorage.getItem("selectedStudent");
    // if (selectedStudentData) {
    //   try {
    //     const studentData = JSON.parse(selectedStudentData);
    //     // 填充表单数据
    //     fillFormWithStudentData(studentData);
    //     // 清除localStorage中的数据，避免下次进入页面时自动填充
    //     localStorage.removeItem("selectedStudent");
    //   } catch (error) {
    //     console.error("解析预选学员数据失败:", error);
    //   }
    // }
  });

  // 加载学科门类数据
  loadDisciplineCategories();
});

// 加载学科门类列表
const loadDisciplineCategories = async () => {
  try {
    const res = await getDisciplineCategories();
    if (res.code === 0) {
      disciplineCategoryOptions.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取学科门类失败");
    }
  } catch (error) {
    console.error("获取学科门类失败", error);
    ElMessage.error("获取学科门类失败，请稍后重试");
  }
};

// 根据学生数据加载对应的学科分类数据
const loadDisciplineDataForStudent = async () => {
  if (reportForm.disciplineCategory) {
    try {
      const res = await getFirstLevelDisciplines(reportForm.disciplineCategory);
      if (res.code === 0) {
        firstLevelDisciplineOptions.value = res.data;
      }
    } catch (error) {
      console.error("加载一级学科失败", error);
    }
  }

  if (reportForm.firstLevelDiscipline) {
    try {
      const res = await getSecondLevelDisciplines(
        reportForm.firstLevelDiscipline
      );
      if (res.code === 0) {
        secondLevelDisciplineOptions.value = res.data;
      }
    } catch (error) {
      console.error("加载二级学科失败", error);
    }
  }
};

// 清理资源
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  window.removeEventListener("student-selected", handleStudentSelected);
});

// 处理学员选择事件
const handleStudentSelected = (event) => {
  const studentData = event.detail;

  console.log("studentData", studentData);
  if (studentData) {
    // 重置报告状态：如果当前有生成的报告，则清空并重置状态
    if (reportContentElement.value && reportContentElement.value.isVisible) {
      console.log("检测到已有报告，开始重置状态");

      // 显示 AI 按钮
      showCreateBtn.value = true;

      // 隐藏并清空 content.vue 组件
      reportContentElement.value.hide();

      // 重置 content.vue 组件的数据（如果有重置方法）
      if (typeof reportContentElement.value.resetData === "function") {
        reportContentElement.value.resetData();
      }

      // 隐藏专业分析组件
      showMajorAnalysis.value = false;

      console.log("报告状态重置完成");
    }

    // 打印完整的学员数据，用于调试
    console.log("选中的学员完整数据:", JSON.stringify(studentData, null, 2));

    // 特别关注 targetProvinces 字段
    console.log(
      "targetProvinces 数据类型:",
      typeof studentData.targetProvinces
    );
    console.log("targetProvinces 值:", studentData.targetProvinces);

    // 填充表单数据
    fillFormWithStudentData(studentData);

    // 填充后检查表单中的省份数据
    console.log("填充后的省份数据:", reportForm.intendedSchools);
    console.log("填充后的区域数据:", reportForm.region);
  }
};

// 填充表单数据
const fillFormWithStudentData = (studentData) => {
  console.log("填充学员数据到表单:", studentData);

  // 基本信息
  (reportForm.student_id = studentData.id || 0),
    (reportForm.name = studentData.name || "");
  reportForm.sex =
    studentData.sex === 1 ? "1" : studentData.sex === 2 ? "2" : "";
  reportForm.phone = studentData.phone || "";
  reportForm.undergraduateSchool = studentData.undergraduateSchool || "";
  reportForm.undergraduateMajor = studentData.undergraduateMajor || "";
  reportForm.undergraduateSchoolName =
    studentData.undergraduateSchoolName || "";

  reportForm.undergraduateMajorName = studentData.undergraduateMajorName || "";
  reportForm.disciplineCategory = studentData.disciplineCategory || null;
  reportForm.firstLevelDiscipline = studentData.firstLevelDiscipline || null;
  reportForm.targetMajor = studentData.targetMajor || "";
  reportForm.targetMajorName = studentData.targetMajorName || "";
  reportForm.majorCode = studentData.majorCode || "";

  // 检查是否需要添加本科学校到选项中
  if (studentData.undergraduateSchool && studentData.undergraduateSchoolName) {
    const existingOption = collegeOptions.value.find(
      (option) => option.value === studentData.undergraduateSchool
    );

    if (!existingOption) {
      // 如果选项中不存在该学校，则添加到选项中
      collegeOptions.value.push({
        value: studentData.undergraduateSchool,
        label: studentData.undergraduateSchoolName,
      });
    }
  }
  // 检查是否需要添加本科专业到选项中
  if (studentData.undergraduateMajor && studentData.undergraduateMajorName) {
    const existingMajorOption = majorOptions.value.find(
      (option) => option.value === studentData.undergraduateMajor
    );

    if (!existingMajorOption) {
      // 如果选项中不存在该专业，则添加到选项中
      majorOptions.value.push({
        value: studentData.undergraduateMajor,
        label: studentData.undergraduateMajorName,
      });
    }
  }

  // 考研信息
  if (studentData.examYear) {
    reportForm.examYear = studentData.examYear.toString();
  }

  reportForm.isMultiDisciplinary =
    studentData.isMultiDisciplinary === 1 ? "1" : "2"; // 确保跨专业字段类型一致

  reportForm.educationalStyle = studentData.educationalStyle === 1 ? "1" : "0"; // 确保培养方式字段类型一致

  // 英语基础
  reportForm.englishScore = studentData.englishScore || "";

  // 四级成绩 - 使用新字段名
  reportForm.cet4 = studentData.cet4 || "";

  // 六级成绩 - 使用新字段名
  reportForm.cet6 = studentData.cet6 || "";

  // 托福成绩 - 使用新字段名
  reportForm.tofelScore = studentData.tofelScore || "";

  // 雅思成绩 - 使用新字段名
  reportForm.ieltsScore = studentData.ieltsScore || "";

  // 英语能力 - 使用新字段名
  reportForm.englishLevel = studentData.englishAbility || "";

  // 清空当前选择的省份
  reportForm.intendedSchools = [];
  if (studentData.targetProvinces) {
    // 如果是数组，直接使用
    if (Array.isArray(studentData.targetProvinces)) {
      // 确保每个省份都存在于 provinceData 中
      const validProvinces = studentData.targetProvinces.filter((province) => {
        return provinceData.some((p) => p.value === province);
      });

      if (validProvinces.length > 0) {
        reportForm.intendedSchools = validProvinces;
        console.log("有效的省份数据:", validProvinces);
      } else {
        console.warn("没有找到有效的省份数据");
      }
    }
    // 如果是字符串，尝试解析
    else if (typeof studentData.targetProvinces === "string") {
      // 首先尝试按逗号分隔
      const provinces = studentData.targetProvinces.split(",");
      // 过滤掉空字符串
      const validProvinces = provinces.filter((p) => {
        const trimmed = p.trim();
        return (
          trimmed !== "" && provinceData.some((prov) => prov.value === trimmed)
        );
      });

      if (validProvinces.length > 0) {
        reportForm.intendedSchools = validProvinces;
        console.log("解析后的有效省份:", validProvinces);
      } else {
        console.warn("解析后没有找到有效的省份数据");
      }
    }
  }

  // 确保省份数据在下拉框中可见
  console.log("设置省份选择:", reportForm.intendedSchools);
  reportForm.region = studentData.targetRegion;
  const intendedSchools = reportForm.intendedSchools;
  // 使用 nextTick 确保在 DOM 更新后再设置省份
  nextTick(() => {
    reportForm.intendedSchools = intendedSchools;
  });
  reportForm.targetSchool = studentData.targetSchool || "";
  reportForm.targetSchoolName = studentData.targetSchoolName || "";

  // 如果有梦校ID但没有梦校名称，添加到dreamSchoolOptions中
  if (reportForm.targetSchool && reportForm.targetSchoolName) {
    dreamSchoolOptions.value = [
      {
        value: reportForm.targetSchool,
        label: reportForm.targetSchoolName,
      },
    ];
  }
  reportForm.schoolLevel = studentData.schoolLevel || "";
  reportForm.referenceBooks = studentData.referenceBooks || "";

  // 成绩情况
  // 使用 undergraduateTranscript 字段
  if (studentData.undergraduateTranscript) {
    try {
      let transcript = studentData.undergraduateTranscript;
      if (typeof transcript === "string") {
        transcript = JSON.parse(transcript);
      }

      if (Array.isArray(transcript) && transcript.length > 0) {
        // 创建新的成绩项
        scoreInfo.value = transcript.map((score, index) => ({
          id: index + 1,
          title: score.title || score.name || `课程${index + 1}`,
          score: score.score || "",
        }));
      }
    } catch (error) {
      console.error("解析undergraduateTranscript失败:", error);
    }
  }

  // 如果没有任何成绩数据，添加默认的成绩项
  if (scoreInfo.value.length === 0) {
    scoreInfo.value = [
      { id: 1, title: "高数(上)", score: "" },
      { id: 2, title: "高数(下)", score: "" },
      { id: 3, title: "概率论", score: "" },
      { id: 4, title: "线性代数", score: "" },
    ];
  }

  // 考研预估 - 字段映射：politics=政治，englishS=英语，englishType=业务课一，mathType=业务课二
  reportForm.politics = studentData.politics || "";
  reportForm.englishS = studentData.englishS || ""; // 英语成绩
  reportForm.englishType = studentData.englishType || ""; // 业务课一
  reportForm.mathType = studentData.mathType || ""; // 业务课二
  reportForm.totalScore = studentData.totalScore || "";

  // 手动触发计算总分
  calculateTotalScore();

  // 打印调试信息
  console.log("填充考研预估成绩:", {
    政治: reportForm.politics,
    英语成绩: reportForm.englishS,
    业务课一: reportForm.englishType,
    业务课二: reportForm.mathType,
    总分: reportForm.totalScore,
  });

  // 自动计算总分（如果有政治、英语、业务课一和业务课二的分数）
  if (
    reportForm.politics &&
    reportForm.englishS &&
    reportForm.englishType &&
    reportForm.mathType
  ) {
    const politics = parseFloat(reportForm.politics) || 0;
    const english = parseFloat(reportForm.englishS) || 0; // 英语成绩
    const businessOne = parseFloat(reportForm.englishType) || 0; // 业务课一
    const businessTwo = parseFloat(reportForm.mathType) || 0; // 业务课二

    reportForm.totalScore = (
      politics +
      english +
      businessOne +
      businessTwo
    ).toString();
  }

  // 个性化需求和薄弱模块
  reportForm.personalNeeds = studentData.personalNeeds || "";
  reportForm.weakModules = studentData.weakModules || "";

  // 加载对应的学科分类数据
  loadDisciplineDataForStudent();

  // 提示用户
  ElMessage.success("学员信息已填充到表单中");
};

// 生成学生基本信息HTML
const generateStudentInfoHtml = () => {
  // 使用固定标题：英语、业务课一、业务课二
  const englishLabel = "英语";
  const businessOneLabel = "业务课一";
  const businessTwoLabel = "业务课二";

  return "";
};

// 获取学校名称的辅助函数
const getSchoolName = (schoolId) => {
  const school = collegeOptions.value.find((item) => item.value === schoolId);
  return school ? school.label : schoolId;
};

// 获取专业名称的辅助函数
const getMajorName = (majorId) => {
  const major = majorOptions.value.find((item) => item.value === majorId);
  return major ? major.label : majorId;
};

// 生成图表配置（与MajorAnalysis组件保持一致）
const generateChartOption = (isARegion = true) => {
  console.log(`生成${isARegion ? "A区" : "B区"}图表配置`);

  // 如果没有传入国家线数据，使用默认数据
  const nationalData = nationalLineData.value || {
    subject_code: "",
    subject_name: "",
    years: [],
    a_total: [],
    a_single_100: [],
    a_single_over100: [],
    b_total: [],
    b_single_100: [],
    b_single_over100: [],
  };

  console.log("国家线数据:", nationalData);

  // 根据区域选择对应的数据
  const years =
    nationalData.years.length > 0
      ? nationalData.years
      : ["2021", "2022", "2023", "2024", "2025"];

  const totalScores = isARegion
    ? nationalData.a_total.length > 0
      ? nationalData.a_total
      : [360, 370, 360, 370, 370]
    : nationalData.b_total.length > 0
    ? nationalData.b_total
    : [340, 350, 340, 350, 350];

  const single100Scores = isARegion
    ? nationalData.a_single_100.length > 0
      ? nationalData.a_single_100
      : [60, 65, 60, 65, 65]
    : nationalData.b_single_100.length > 0
    ? nationalData.b_single_100
    : [55, 60, 55, 60, 60];

  const singleOver100Scores = isARegion
    ? nationalData.a_single_over100.length > 0
      ? nationalData.a_single_over100
      : [90, 95, 90, 95, 95]
    : nationalData.b_single_over100.length > 0
    ? nationalData.b_single_over100
    : [85, 90, 85, 90, 90];

  console.log(`${isARegion ? "A区" : "B区"}图表数据:`, {
    years,
    totalScores,
    single100Scores,
    singleOver100Scores,
  });

  // 构建标题
  const regionText = isARegion ? "A区" : "B区";

  // 获取专业名称、代码和一级学科
  let majorName = "";
  let majorCode = "";
  let firstLevelSubject = "";

  if (reportForm.targetMajor) {
    majorName = reportForm.targetMajor;
  }
  if (reportForm.majorCode) {
    majorCode = reportForm.majorCode;
  }
  if (reportForm.majorCode) {
    firstLevelSubject = getFirstLevelSubject(reportForm.majorCode);
  }

  // 构建标题，添加一级学科列
  const chartTitle = `${regionText}  专业名称：${majorName}  专业代码：${majorCode}  一级学科：${firstLevelSubject}`;

  return {
    title: {
      text: chartTitle,
      left: "left",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#1bb394",
      },
      top: 10,
    },
    grid: {
      left: 40,
      right: 60,
      top: 70,
      bottom: 50,
      containLabel: true,
    },
    legend: {
      data: ["总分", "单科(满分=100)", "单科(满分>100)"],
      right: 10,
      top: 10,
      icon: "rect",
      itemWidth: 16,
      itemHeight: 8,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,0.7)",
      borderRadius: 8,
      textStyle: { color: "#fff" },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: years,
      axisLine: { lineStyle: { color: "#1bb394" } },
      axisLabel: { color: "#666" },
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 400,
      interval: 50,
      splitNumber: 8,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#eee",
          type: "dashed",
        },
      },
      axisLine: { show: false },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    series: [
      {
        name: "总分",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 153, 0, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#ff9900", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff9900",
          fontWeight: "bold",
          offset: [0, -20],
          formatter: function (params) {
            return params.value;
          },
        },
        data: totalScores,
      },
      {
        name: "单科(满分=100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(27, 179, 148, 0.15)",
          origin: "start",
        },
        lineStyle: { color: "#1bb394", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#1bb394",
          fontWeight: "bold",
          offset: [0, -10],
          formatter: function (params) {
            return params.value;
          },
        },
        data: single100Scores,
      },
      {
        name: "单科(满分>100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: "rgba(255, 99, 132, 0.10)",
          origin: "start",
        },
        lineStyle: { color: "#ff6384", width: 2 },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff6384",
          fontWeight: "bold",
          offset: [0, 5],
          formatter: function (params) {
            return params.value;
          },
        },
        data: singleOver100Scores,
      },
    ],
  };
};

// 重新创建预览中的图表
const recreateChartsInPreview = async () => {
  console.log("开始重新创建预览中的图表");
  console.log("当前专业分析显示状态:", showMajorAnalysis.value);
  console.log("专业分析组件引用:", majorAnalysisRef.value);
  console.log("国家线数据:", nationalLineData.value);
  console.log("表单数据:", {
    targetMajor: reportForm.targetMajor,
    majorCode: reportForm.majorCode,
    targetSchoolName: reportForm.targetSchoolName,
  });

  try {
    // 动态导入echarts
    const echarts = await import("echarts");
    console.log("echarts导入成功");

    // 1. 处理专业分析组件中的图表
    if (showMajorAnalysis.value) {
      console.log("开始处理专业分析图表");
      await recreateMajorAnalysisCharts(echarts);
    } else {
      console.log("专业分析未显示，跳过专业分析图表处理");
    }

    // 2. 处理报告内容中的图表
    console.log("开始处理报告内容图表");
    await recreateContentCharts(echarts);

    console.log("所有图表重新创建完成");

    // 返回成功状态，用于PDF生成时判断
    return true;
  } catch (error) {
    console.error("重新创建图表失败:", error);
    ElMessage.error("图表重新创建失败: " + error.message);
    return false;
  }
};

// 重新创建专业分析图表
const recreateMajorAnalysisCharts = async (echarts) => {
  console.log("开始重新创建专业分析图表");

  // 查找预览容器中的专业分析图表容器
  const previewMajorCharts = previewContainer.value?.querySelectorAll(
    '.major-chart, [id*="preview-major-chart"]'
  );
  console.log(
    "预览中找到的专业分析图表容器数量:",
    previewMajorCharts?.length || 0
  );

  if (!previewMajorCharts || previewMajorCharts.length === 0) {
    console.log("预览中没有找到专业分析图表容器");
    return;
  }

  // 确保我们有专业分析组件的引用
  if (!majorAnalysisRef.value) {
    console.log("没有专业分析组件引用，使用备用方法");
    // 如果没有组件引用，使用备用的图表配置生成方法
    await recreateMajorAnalysisChartsBackup(echarts, previewMajorCharts);
    return;
  }

  // 为每个预览专业分析图表容器重新创建图表
  for (let i = 0; i < previewMajorCharts.length; i++) {
    const previewChartContainer = previewMajorCharts[i];

    console.log(`重新创建第${i + 1}个专业分析图表`);

    // 检查容器是否存在且有正确的尺寸
    if (!previewChartContainer) {
      console.warn(`第${i + 1}个图表容器不存在`);
      continue;
    }

    // 设置预览容器样式
    previewChartContainer.style.width = "100%";
    previewChartContainer.style.height = "280px";
    previewChartContainer.style.display = "block";
    previewChartContainer.style.visibility = "visible";

    // 等待容器渲染完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 检查容器尺寸
    const rect = previewChartContainer.getBoundingClientRect();
    console.log(`第${i + 1}个图表容器尺寸:`, rect);

    if (rect.width === 0 || rect.height === 0) {
      console.warn(`第${i + 1}个图表容器尺寸为0，跳过`);
      continue;
    }

    try {
      // 根据索引生成对应的图表配置（A区或B区）
      const isARegion = i === 0; // 第一个是A区，第二个是B区

      // 使用专业分析组件的方法获取图表配置
      const chartOption = majorAnalysisRef.value.getChartOption(isARegion);

      console.log(`第${i + 1}个图表配置:`, chartOption);

      // 如果容器中已有图表实例，先销毁
      if (previewChartContainer._echarts_instance_) {
        previewChartContainer._echarts_instance_.dispose();
        delete previewChartContainer._echarts_instance_;
      }

      // 在预览容器中创建新的图表实例
      const newChart = echarts.init(previewChartContainer);
      newChart.setOption(chartOption);

      // 保存图表实例引用
      previewChartContainer._echarts_instance_ = newChart;

      console.log(`第${i + 1}个专业分析图表重新创建完成`);

      // 等待一下再处理下一个
      await new Promise((resolve) => setTimeout(resolve, 200));
    } catch (error) {
      console.error(`重新创建第${i + 1}个专业分析图表失败:`, error);
      // 如果使用组件方法失败，尝试使用备用方法
      try {
        const isARegion = i === 0;
        const chartOption = generateChartOption(isARegion);

        if (previewChartContainer._echarts_instance_) {
          previewChartContainer._echarts_instance_.dispose();
          delete previewChartContainer._echarts_instance_;
        }

        const newChart = echarts.init(previewChartContainer);
        newChart.setOption(chartOption);
        previewChartContainer._echarts_instance_ = newChart;

        console.log(`第${i + 1}个专业分析图表使用备用方法创建完成`);
      } catch (backupError) {
        console.error(`备用方法也失败:`, backupError);
      }
    }
  }
};

// 备用的图表重新创建方法
const recreateMajorAnalysisChartsBackup = async (
  echarts,
  previewMajorCharts
) => {
  console.log("使用备用方法重新创建专业分析图表");

  for (let i = 0; i < previewMajorCharts.length; i++) {
    const previewChartContainer = previewMajorCharts[i];

    if (!previewChartContainer) continue;

    previewChartContainer.style.width = "100%";
    previewChartContainer.style.height = "280px";
    previewChartContainer.style.display = "block";
    previewChartContainer.style.visibility = "visible";

    await new Promise((resolve) => setTimeout(resolve, 100));

    const rect = previewChartContainer.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) continue;

    try {
      const isARegion = i === 0;
      const chartOption = generateChartOption(isARegion);

      if (previewChartContainer._echarts_instance_) {
        previewChartContainer._echarts_instance_.dispose();
        delete previewChartContainer._echarts_instance_;
      }

      const newChart = echarts.init(previewChartContainer);
      newChart.setOption(chartOption);
      previewChartContainer._echarts_instance_ = newChart;

      console.log(`备用方法：第${i + 1}个专业分析图表创建完成`);
      await new Promise((resolve) => setTimeout(resolve, 200));
    } catch (error) {
      console.error(`备用方法：第${i + 1}个图表创建失败:`, error);
    }
  }
};

// 重新创建报告内容图表
const recreateContentCharts = async (echarts) => {
  console.log("开始重新创建报告内容图表");

  // 获取原始报告内容图表容器
  const originalContentCharts = document.querySelectorAll(
    ".content-container [_echarts_instance_]"
  );
  console.log("找到原始报告内容图表数量:", originalContentCharts.length);

  if (originalContentCharts.length === 0) {
    console.log("没有找到原始报告内容图表");
    return;
  }

  // 查找预览容器中对应的报告内容图表容器
  const previewContentCharts = previewContainer.value?.querySelectorAll(
    '.content-chart, [id*="preview-content-chart"]'
  );
  console.log(
    "预览中找到的报告内容图表容器数量:",
    previewContentCharts?.length || 0
  );

  if (!previewContentCharts || previewContentCharts.length === 0) {
    console.log("预览中没有找到报告内容图表容器");
    return;
  }

  // 为每个预览报告内容图表容器重新创建图表
  for (
    let i = 0;
    i < Math.min(originalContentCharts.length, previewContentCharts.length);
    i++
  ) {
    const originalChart = originalContentCharts[i];
    const previewChartContainer = previewContentCharts[i];

    if (originalChart._echarts_instance_ && previewChartContainer) {
      console.log(`重新创建第${i + 1}个报告内容图表`);

      // 设置预览容器样式
      previewChartContainer.style.width = "100%";
      previewChartContainer.style.height = "400px";
      previewChartContainer.style.display = "block";
      previewChartContainer.style.visibility = "visible";

      // 获取原始图表的配置
      const originalOption = originalChart._echarts_instance_.getOption();

      // 在预览容器中创建新的图表实例
      const newChart = echarts.init(previewChartContainer);
      newChart.setOption(originalOption);

      // 保存图表实例引用
      previewChartContainer._echarts_instance_ = newChart;

      console.log(`第${i + 1}个报告内容图表重新创建完成`);

      // 等待一下再处理下一个
      await new Promise((resolve) => setTimeout(resolve, 300));
    }
  }
};
const previewPdfRef = ref(null);
// 预览报告功能
const previewReport = async () => {
  //generateReportId.value = 651;
  // 检查是否有生成的报告ID
  if (!generateReportId.value) {
    ElMessage.warning("请先生成报告内容");
    return;
  }

  previewPdfDialogVisible.value = true;

  // 等待DOM更新和组件挂载
  await nextTick();

  // 等待一小段时间确保组件完全挂载
  await new Promise((resolve) => setTimeout(resolve, 100));

  console.log("previewPdfRef.value:", previewPdfRef.value);
  console.log(reportForm.firstLevelDiscipline);

  try {
    // 使用实际生成的报告ID加载数据
    await previewPdfRef.value.initPdfData(
      generateReportId.value,
      reportForm.firstLevelDiscipline
    );
    console.log("PDF数据初始化成功，报告ID:", generateReportId.value);
  } catch (error) {
    console.error("PDF数据初始化失败:", error);
    ElMessage.error("加载报告数据失败: " + (error.message || "未知错误"));
    previewPdfDialogVisible.value = false;
  }

  return; // 检查是否有报告内容可以预览
  if (!reportContentElement.value || !reportContentElement.value.isVisible) {
    ElMessage.warning("请先生成报告内容");
    return;
  }

  previewDialogVisible.value = true;
  previewLoading.value = true;
  previewContent.value = "";

  try {
    // 创建完整的预览内容，包括学生基本信息、专业分析和报告内容
    let fullPreviewContent = "";

    // 1. 获取学生基本信息（从当前表单）
    const studentInfoHtml = generateStudentInfoHtml();
    console.log("学生基本信息HTML长度:", studentInfoHtml.length);

    // 2. 生成专业分析内容（手动生成，不依赖DOM克隆）
    let majorAnalysisHtml = "";
    if (showMajorAnalysis.value && majorAnalysisRef.value) {
      console.log("开始生成专业分析HTML");

      // 手动生成专业分析的HTML结构
      majorAnalysisHtml = `
        <div class="major-analysis-container" style="width: 100%; padding: 0 20px;">
          <div class="step-section" style="margin-bottom: 30px; background-color: #fff; border-radius: 8px; overflow: hidden;">
            <div class="step-header" style="padding: 15px 20px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; background-image: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/step-bg.png'); background-repeat: no-repeat; padding-left: 92px; color: #fff; font-weight: bold;">
              第二部分：专业分析
            </div>
            <div class="step-content" style="width: 100%; padding: 20px;">
              <div id="preview-major-chart-0" class="echarts-box preview-chart major-chart" style="width: 98%; height: 280px; margin: 0 auto 18px auto; border: 1px solid #1bb394; border-radius: 12px; background: #fff; box-sizing: border-box;"></div>
              <div id="preview-major-chart-1" class="echarts-box preview-chart major-chart" style="width: 98%; height: 280px; margin: 0 auto 18px auto; border: 1px solid #1bb394; border-radius: 12px; background: #fff; box-sizing: border-box;"></div>
            </div>
          </div>
        </div>
      `;
      console.log("专业分析HTML长度:", majorAnalysisHtml.length);
    }

    // 3. 获取报告内容
    const reportContainer = document.querySelector(".content-container");
    if (!reportContainer) {
      throw new Error("找不到报告内容");
    }

    console.log("找到报告容器，子元素数量:", reportContainer.children.length);

    // 克隆内容并处理
    const clonedContainer = reportContainer.cloneNode(true);

    // 移除保存按钮等不需要预览的元素
    const saveButtons = clonedContainer.querySelectorAll(
      ".save-report-container, .save-report-btn"
    );
    saveButtons.forEach((button) => button.remove());

    // 移除编辑按钮
    const editButtons = clonedContainer.querySelectorAll(
      ".edit-btn, button[type='text'][size='small']"
    );
    editButtons.forEach((button) => button.remove());

    // 确保图表容器有正确的ID和类名
    const chartContainers = clonedContainer.querySelectorAll(
      "[_echarts_instance_]"
    );
    chartContainers.forEach((container, index) => {
      // 清除原有的echarts实例引用，避免冲突
      delete container._echarts_instance_;

      // 设置唯一ID用于后续重新创建图表
      container.id = `preview-content-chart-${index}`;
      container.className += " echarts-box preview-chart content-chart";

      // 设置基本样式
      container.style.width = "100%";
      container.style.height = "400px";
      container.style.display = "block";
      container.style.visibility = "visible";
    });

    console.log("报告内容处理完成，图表容器数量:", chartContainers.length);

    // 4. 组合完整的预览内容
    fullPreviewContent = `
      <div class="full-report-preview">
        <div class="student-basic-info" style="padding:0 160px;">
          <h2 style="color: #1bb394; margin-bottom: 20px; text-align: center;">学员基本信息</h2>
          ${studentInfoHtml}
        </div>
        ${
          majorAnalysisHtml
            ? `<div class="major-analysis-section" style="padding:0 160px;">${majorAnalysisHtml}</div>`
            : ""
        }
        <div class="report-content">
          ${clonedContainer.outerHTML}
        </div>
      </div>
    `;

    console.log("完整预览内容长度:", fullPreviewContent.length);
    console.log("预览内容前1000字符:", fullPreviewContent.substring(0, 1000));

    // 设置预览内容
    previewContent.value = fullPreviewContent;

    // 等待DOM更新后重新渲染图表
    await nextTick();

    // 重新初始化预览容器中的echarts图表
    setTimeout(async () => {
      console.log("开始处理预览中的echarts图表");

      // 等待一下确保DOM完全渲染
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 重新创建所有图表
      await recreateChartsInPreview();
    }, 600);
  } catch (error) {
    console.error("预览失败:", error);
    ElMessage.error("预览失败: " + error.message);
    previewContent.value = "<p>预览内容加载失败</p>";
  } finally {
    previewLoading.value = false;
  }
};

// 导出报告功能
const exportReport = async () => {
  if (!generateReportId.value && pdfStore.pdfUrl.value) {
    window.open(pdfStore.pdfUrl.value, "_blank");
  } else {
    ElMessage.warning("请先预览报告生成PDF文件");
  }
};

// 从预览生成PDF
const generatePDFFromPreview = async () => {
  if (!previewContent.value) {
    ElMessage.warning("没有可生成PDF的内容");
    return;
  }

  // 设置loading状态
  pdfGenerating.value = true;

  try {
    ElMessage.info("正在准备PDF内容，请稍候...");

    // 先尝试生成简化版本测试
    console.log("开始测试简化PDF生成...");
    try {
      // 直接在这里生成测试PDF
      console.log("开始生成测试PDF");

      // 使用jsPDF直接生成简单内容
      const { jsPDF } = await import("jspdf");
      const pdf = new jsPDF("p", "mm", "a4");

      // 方案1：使用英文内容测试
      pdf.setFontSize(20);
      pdf.text("Test Report", 20, 30);

      pdf.setFontSize(14);
      pdf.text("This is a test PDF document", 20, 50);
      pdf.text("Used to verify PDF generation functionality", 20, 70);

      pdf.setFontSize(12);
      pdf.text("Student Name: " + (reportForm.name || "Not filled"), 20, 100);
      pdf.text(
        "Target Major: " + (reportForm.targetMajor || "Not filled"),
        20,
        120
      );
      pdf.text(
        "Major Code: " + (reportForm.majorCode || "Not filled"),
        20,
        140
      );

      // 添加一些表格内容
      pdf.text("Score Estimation:", 20, 170);
      pdf.text("Politics: " + (reportForm.politics || "0"), 30, 190);
      pdf.text("English: " + (reportForm.englishType || "0"), 30, 210);
      pdf.text("Math: " + (reportForm.mathType || "0"), 30, 230);
      pdf.text("Professional: " + (reportForm.professional || "0"), 30, 250);

      // 添加第二页
      pdf.addPage();
      pdf.setFontSize(16);
      pdf.text("Page 2 Content", 20, 30);
      pdf.setFontSize(12);
      pdf.text("This is the second page test content", 20, 50);
      pdf.text(
        "If this PDF displays normally, basic functionality works",
        20,
        70
      );

      // 添加一些数字和符号测试
      pdf.text("Numbers: 0123456789", 20, 100);
      pdf.text("Symbols: !@#$%^&*()_+-=[]{}|;:,.<>?", 20, 120);
      pdf.text("Date: " + new Date().toLocaleDateString(), 20, 140);

      const testPdfBlob = pdf.output("blob");
      console.log("测试PDF生成成功，大小:", testPdfBlob.size, "bytes");

      if (testPdfBlob && testPdfBlob.size > 1000) {
        console.log("测试PDF生成成功，现在生成完整样式PDF");

        // 不下载测试PDF，直接生成完整版本
        // 测试PDF成功，现在生成完整版本
        await generateFullStyledPDF();
        return;
      }
    } catch (testError) {
      console.error("测试PDF生成失败:", testError);
    }

    // 如果测试PDF失败，仍然尝试生成完整版本
    await generateFullStyledPDF();
  } catch (error) {
    console.error("PDF生成失败:", error);
    ElMessage.error("PDF生成失败: " + error.message);
  } finally {
    // 确保loading状态被重置
    pdfGenerating.value = false;
  }
};

// 生成完整样式的PDF
const generateFullStyledPDF = async () => {
  try {
    console.log("开始生成完整样式PDF...");
    ElMessage.info("正在生成完整样式PDF，保留前端效果...");

    // 确保图表已经正确渲染
    console.log("开始确保图表正确渲染");
    const chartsReady = await recreateChartsInPreview();

    if (!chartsReady) {
      console.warn("图表渲染可能有问题，但继续生成PDF");
    }

    // 等待图表完全渲染
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 创建临时容器用于生成PDF
    const tempContainer = document.createElement("div");
    tempContainer.style.position = "fixed";
    tempContainer.style.left = "50%";
    tempContainer.style.transform = "translateX(-50%)";
    tempContainer.style.top = "0";
    tempContainer.style.width = "1200px";
    tempContainer.style.height = "auto";
    tempContainer.style.backgroundColor = "white";
    tempContainer.style.zIndex = "9999"; // 提高层级确保可见
    tempContainer.style.overflow = "visible";
    tempContainer.style.visibility = "visible";
    tempContainer.style.display = "block";

    // 设置预览内容到临时容器
    tempContainer.innerHTML = previewContent.value;

    // 移除编辑按钮
    const editButtons = tempContainer.querySelectorAll(
      ".edit-btn, button[type='text'][size='small']"
    );
    editButtons.forEach((button) => button.remove());

    // 添加到页面
    document.body.appendChild(tempContainer);

    try {
      // 等待DOM渲染
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 调试：检查容器内容
      console.log("临时容器子元素数量:", tempContainer.children.length);
      console.log("临时容器滚动高度:", tempContainer.scrollHeight);
      console.log("临时容器内容长度:", tempContainer.innerHTML.length);

      // 确保容器中所有元素都可见
      const allElements = tempContainer.querySelectorAll("*");
      allElements.forEach((el) => {
        if (el.style) {
          el.style.visibility = "visible";
          el.style.opacity = "1";
          if (el.style.display === "none") {
            el.style.display = "block";
          }
        }
      });

      // 特别处理图表容器
      const chartElements = tempContainer.querySelectorAll(
        '[id*="preview-"], .echarts-box, .preview-chart'
      );
      chartElements.forEach((container) => {
        container.style.width = "100%";
        container.style.height = "400px";
        container.style.display = "block";
        container.style.visibility = "visible";
        container.style.opacity = "1";
        container.style.backgroundColor = "#fff";
      });

      // 特别处理school-tags样式，确保在PDF中居中
      const schoolTagsElements = tempContainer.querySelectorAll(".school-tags");
      schoolTagsElements.forEach((tagsEl) => {
        tagsEl.style.position = "absolute";
        tagsEl.style.top = "40px";
        tagsEl.style.left = "0";
        tagsEl.style.right = "0";
        tagsEl.style.display = "flex";
        tagsEl.style.gap = "4px";
        tagsEl.style.justifyContent = "center";
        tagsEl.style.alignItems = "center";
        tagsEl.style.width = "100%";
        tagsEl.style.textAlign = "center";
      });

      // 处理网络图片转base64
      await convertNetworkImagesToBase64(tempContainer);

      // 重新创建临时容器中的图表
      await recreateChartsInContainer(tempContainer);

      // 再等待图表渲染
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 处理网络图片转base64 - 确保所有图片都能在PDF中正常显示
      console.log("开始处理网络图片，确保PDF兼容性...");
      await convertNetworkImagesToBase64(tempContainer);

      // 图片处理完成后，等待一段时间确保所有处理都完成
      console.log("图片处理完成，等待stabilization...");
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // 验证关键图片是否已转换
      const stepNumTags = tempContainer.querySelectorAll(".step-num-tag");
      stepNumTags.forEach((element, index) => {
        const bgImage = element.style.backgroundImage;
        if (bgImage && bgImage.includes("data:image")) {
          console.log(`step-num-tag ${index + 1} 背景图片已转换为base64`);
        } else {
          console.warn(`step-num-tag ${index + 1} 背景图片未转换:`, bgImage);
        }
      });

      // 强制设置所有内联样式，确保PDF渲染正确
      console.log("开始强制设置内联样式...");
      forceInlineStyles(tempContainer);

      // 等待样式应用完成
      console.log("等待样式应用完成...");
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log(
        "开始使用html2canvas截图，容器尺寸:",
        tempContainer.scrollWidth,
        "x",
        tempContainer.scrollHeight
      );

      // 调试：暂时设置容器为可见以验证内容
      tempContainer.style.opacity = "1";
      tempContainer.style.zIndex = "9999";
      tempContainer.style.backgroundColor = "#ffffff";

      // 等待一下确保样式生效
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 动态导入html2canvas
      const html2canvas = await import("html2canvas");
      const canvas = await html2canvas.default(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: tempContainer.scrollWidth || 1200,
        height: tempContainer.scrollHeight || 800,
        logging: true, // 开启日志以便调试
        removeContainer: false,
        foreignObjectRendering: false, // 禁用foreignObject渲染，使用传统DOM渲染
        imageTimeout: 30000, // 增加图片超时时间到30秒
        proxy: undefined, // 禁用代理
        onclone: (clonedDoc) => {
          console.log("开始克隆文档进行截图");
          const clonedContainer = clonedDoc.querySelector("div");
          if (clonedContainer) {
            clonedContainer.style.width = "1200px";
            clonedContainer.style.display = "block";
            clonedContainer.style.visibility = "visible";
            clonedContainer.style.opacity = "1";
            clonedContainer.style.backgroundColor = "#ffffff";

            // 确保所有元素可见
            const allElements = clonedContainer.querySelectorAll("*");
            allElements.forEach((el) => {
              if (el.style) {
                el.style.visibility = "visible";
                el.style.opacity = "1";
                if (el.style.display === "none") {
                  el.style.display = "block";
                }
                // 确保有背景色
                if (el.tagName === "DIV" && !el.style.backgroundColor) {
                  el.style.backgroundColor = "transparent";
                }
              }
            });

            // 特别处理图表容器
            const chartElements = clonedContainer.querySelectorAll(
              '[id*="preview-"], .echarts-box, .preview-chart'
            );
            chartElements.forEach((container) => {
              container.style.width = "100%";
              container.style.height = "400px";
              container.style.display = "block";
              container.style.visibility = "visible";
              container.style.opacity = "1";
              container.style.backgroundColor = "#fff";
            });

            // 特别处理school-tags样式，确保在PDF中居中
            const schoolTagsElements =
              clonedContainer.querySelectorAll(".school-tags");
            schoolTagsElements.forEach((tagsEl) => {
              tagsEl.style.position = "absolute";
              tagsEl.style.top = "40px";
              tagsEl.style.left = "0";
              tagsEl.style.right = "0";
              tagsEl.style.display = "flex";
              tagsEl.style.gap = "4px";
              tagsEl.style.justifyContent = "center";
              tagsEl.style.alignItems = "center";
              tagsEl.style.width = "100%";
              tagsEl.style.textAlign = "center";
            });

            // 强制处理所有step-num-tag的背景图片
            const clonedStepNumTags = clonedContainer.querySelectorAll(
              '[class*="step-num-tag"]'
            );
            const originalStepNumTags = tempContainer.querySelectorAll(
              '[class*="step-num-tag"]'
            );

            console.log(
              `克隆文档中找到 ${clonedStepNumTags.length} 个step-num-tag元素`
            );
            console.log(
              `原始容器中找到 ${originalStepNumTags.length} 个step-num-tag元素`
            );

            clonedStepNumTags.forEach((clonedEl, index) => {
              // 强制设置背景图片样式
              clonedEl.style.width = "265px";
              clonedEl.style.height = "40px";
              clonedEl.style.backgroundSize = "100% 100%";
              clonedEl.style.backgroundRepeat = "no-repeat";
              clonedEl.style.backgroundPosition = "center";
              clonedEl.style.display = "flex";
              clonedEl.style.alignItems = "center";
              clonedEl.style.marginBottom = "20px";

              // 从对应的原始元素复制背景图片
              if (
                originalStepNumTags[index] &&
                originalStepNumTags[index].style.backgroundImage
              ) {
                clonedEl.style.backgroundImage =
                  originalStepNumTags[index].style.backgroundImage;
                console.log(
                  `设置step-num-tag ${index + 1} 背景图片:`,
                  clonedEl.style.backgroundImage.substring(0, 100) + "..."
                );
              } else {
                // 如果没有找到对应的背景图片，使用默认的base64图片
                console.warn(
                  `step-num-tag ${
                    index + 1
                  } 没有找到背景图片，查找所有已转换的背景图片`
                );
                // 尝试从临时容器中的任意一个step-num-tag获取背景图片
                const anyStepTag = tempContainer.querySelector(
                  '[class*="step-num-tag"]'
                );
                if (anyStepTag && anyStepTag.style.backgroundImage) {
                  clonedEl.style.backgroundImage =
                    anyStepTag.style.backgroundImage;
                  console.log(
                    `使用备用背景图片:`,
                    clonedEl.style.backgroundImage.substring(0, 100) + "..."
                  );
                }
              }
            });

            // 强制处理所有图片元素，确保src正确设置
            const clonedImages = clonedContainer.querySelectorAll("img");
            const originalImages = tempContainer.querySelectorAll("img");

            console.log(`克隆文档中找到 ${clonedImages.length} 个img元素`);
            console.log(`原始容器中找到 ${originalImages.length} 个img元素`);

            clonedImages.forEach((clonedImg, index) => {
              // 确保图片的基本样式
              clonedImg.style.display = "block";
              clonedImg.style.visibility = "visible";
              clonedImg.style.opacity = "1";

              // 从对应的原始图片复制src（可能已经是base64）
              if (originalImages[index]) {
                clonedImg.src = originalImages[index].src;
                console.log(
                  `设置img ${index + 1} src:`,
                  clonedImg.src.substring(0, 100) + "..."
                );
              }

              // 如果是COS图片且还是网络地址，强制设置可能的错误处理
              if (
                clonedImg.src &&
                clonedImg.src.includes(
                  "yqzx-1300870289.cos.ap-nanjing.myqcloud.com"
                )
              ) {
                clonedImg.crossOrigin = "anonymous";
                clonedImg.referrerPolicy = "no-referrer";
                console.log(`为COS图片设置跨域属性:`, clonedImg.src);
              }
            });

            // 强制处理recommend-icon中的图片
            const recommendIcons = clonedContainer.querySelectorAll(
              '[class*="recommend-icon"] img'
            );
            recommendIcons.forEach((img, index) => {
              img.style.width = "27px";
              img.style.height = "21px";
              img.style.display = "inline-block";
              img.style.visibility = "visible";
              img.style.opacity = "1";
              console.log(
                `强制设置recommend-icon图片 ${index + 1}:`,
                img.src.substring(0, 100) + "..."
              );
            });

            console.log("克隆容器设置完成");
          }
        },
      });

      // 验证canvas
      if (!canvas || canvas.width === 0 || canvas.height === 0) {
        throw new Error("Canvas生成失败或尺寸无效");
      }

      console.log("Canvas尺寸:", canvas.width, "x", canvas.height);

      // 获取图片数据并验证
      const imgData = canvas.toDataURL("image/jpeg", 0.95);
      console.log("图片数据长度:", imgData.length);

      // 验证图片数据是否有效（检查是否是空白图片）
      if (!imgData || imgData === "data:," || imgData.length < 1000) {
        throw new Error("图片数据生成失败或图片为空");
      }

      // 调试：检查图片数据的开头，确保不是空白图片
      const imgDataHeader = imgData.substring(0, 100);
      console.log("图片数据开头:", imgDataHeader);

      // 创建一个临时的img元素来验证图片数据
      const testImg = new Image();
      await new Promise((resolve, reject) => {
        testImg.onload = () => {
          console.log(
            "测试图片加载成功，尺寸:",
            testImg.width,
            "x",
            testImg.height
          );
          resolve();
        };
        testImg.onerror = () => {
          console.error("测试图片加载失败");
          reject(new Error("图片数据无效"));
        };
        testImg.src = imgData;
      });

      // 使用jsPDF生成PDF
      const { jsPDF } = await import("jspdf");
      const pdf = new jsPDF("l", "mm", "a3"); // 使用A3横向，更好地容纳内容

      const pageWidth = 420;
      const pageHeight = 297;
      const pageBottomMargin = 25; // 底部预留空间
      const actualPageHeight = pageHeight - pageBottomMargin;

      // 计算图片在PDF中的尺寸
      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      console.log("PDF参数:", {
        pageWidth,
        pageHeight,
        imgWidth,
        imgHeight,
        pageBottomMargin,
      });

      let currentPage = 1;
      let yPosition = 0;

      // 如果图片高度小于等于页面高度，直接放在一页
      if (imgHeight <= actualPageHeight) {
        pdf.addImage(imgData, "JPEG", 0, 0, imgWidth, imgHeight);

        // 添加页码（纯数字格式）
        pdf.setFontSize(10);
        pdf.setTextColor(128);
        pdf.text(`${currentPage}`, pageWidth - 20, pageHeight - 10);
      } else {
        // 图片需要分页显示
        let remainingHeight = imgHeight;
        let sourceY = 0;

        while (remainingHeight > 0) {
          // 计算当前页面可以显示的高度
          const pageContentHeight = Math.min(remainingHeight, actualPageHeight);

          // 计算在canvas中对应的高度
          const canvasSourceY = Math.round(
            (sourceY / imgHeight) * canvas.height
          );
          const canvasHeight = Math.round(
            (pageContentHeight / imgHeight) * canvas.height
          );

          // 创建临时canvas来裁剪图片
          const tempCanvas = document.createElement("canvas");
          const tempCtx = tempCanvas.getContext("2d");
          tempCanvas.width = canvas.width;
          tempCanvas.height = canvasHeight;

          // 从原始canvas中提取当前页面的内容
          tempCtx.drawImage(
            canvas,
            0,
            canvasSourceY,
            canvas.width,
            canvasHeight,
            0,
            0,
            canvas.width,
            canvasHeight
          );

          // 将裁剪后的内容添加到PDF
          const pageImgData = tempCanvas.toDataURL("image/jpeg", 0.95);

          if (currentPage > 1) {
            pdf.addPage();
          }

          pdf.addImage(pageImgData, "JPEG", 0, 0, imgWidth, pageContentHeight);

          // 添加页码（纯数字格式）
          pdf.setFontSize(10);
          pdf.setTextColor(128);
          pdf.text(`${currentPage}`, pageWidth - 20, pageHeight - 10);

          // 更新变量
          sourceY += pageContentHeight;
          remainingHeight -= pageContentHeight;
          currentPage++;

          console.log(
            `生成第 ${currentPage - 1} 页，剩余高度: ${remainingHeight}`
          );
        }
      }

      console.log("完整样式PDF生成完成，页数:", pdf.getNumberOfPages());

      // 生成PDF Blob
      const pdfBlob = pdf.output("blob");
      console.log("PDF文件大小:", pdfBlob.size, "bytes");

      // 验证PDF大小
      if (pdfBlob.size < 50000) {
        console.warn("PDF文件较小，可能内容有问题");
      }

      // 生成包含学员姓名的文件名
      const studentName = reportForm.name || "未知学员";
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      const fileName = `${studentName}_考研分析报告_${timestamp}.pdf`;

      // 创建下载链接并触发下载
      const downloadUrl = URL.createObjectURL(pdfBlob);

      // 保存PDF到cos
      const PDFfile = new File([pdfBlob], fileName, {
        type: "application/pdf",
      });
      ElMessage.info("正在上传PDF到COS，请稍候...");
      const result = await upload(PDFfile);
      if (typeof result.Location == "string") {
        await updatePdfUrl({
          report_id: generateReportId.value,
          pdf_url: "https://" + result.Location,
        });
        pdfUrl.value = "https://" + result.Location;
        ElMessage.success("PDF上传成功");
      }
      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;
      downloadLink.style.display = "none";

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadUrl);

      ElMessage.success(`完整样式PDF下载成功！文件名: ${fileName}`);
    } catch (error) {
      console.error("完整PDF生成失败:", error);
      ElMessage.error("完整PDF生成失败: " + error.message);
    } finally {
      // 清理临时容器
      if (tempContainer.parentNode) {
        document.body.removeChild(tempContainer);
      }
    }
  } catch (error) {
    console.error("生成完整样式PDF失败:", error);
    ElMessage.error("生成完整样式PDF失败: " + error.message);
  }
};

// 在指定容器中重新创建图表
const recreateChartsInContainer = async (container) => {
  console.log("在容器中重新创建图表");

  try {
    // 移除编辑按钮
    const editButtons = container.querySelectorAll(
      ".edit-btn, button[type='text'][size='small']"
    );
    editButtons.forEach((button) => button.remove());

    // 处理网络图片转base64
    await convertNetworkImagesToBase64(container);

    // 动态导入echarts
    const echarts = await import("echarts");

    // 查找容器中的图表元素
    const chartContainers = container.querySelectorAll(
      '[id*="preview-"], .echarts-box, .preview-chart'
    );
    console.log("找到图表容器数量:", chartContainers.length);

    for (let i = 0; i < chartContainers.length; i++) {
      const chartContainer = chartContainers[i];

      // 设置容器样式
      chartContainer.style.width = "100%";
      chartContainer.style.height = "400px";
      chartContainer.style.display = "block";
      chartContainer.style.visibility = "visible";
      chartContainer.style.opacity = "1";
      chartContainer.style.backgroundColor = "#fff";

      // 等待容器就绪
      await new Promise((resolve) => setTimeout(resolve, 100));

      try {
        let chartOption = null;

        // 尝试从预览容器中的对应图表获取配置
        const previewCharts = previewContainer.value?.querySelectorAll(
          "[_echarts_instance_]"
        );
        if (
          previewCharts &&
          previewCharts[i] &&
          previewCharts[i]._echarts_instance_
        ) {
          chartOption = previewCharts[i]._echarts_instance_.getOption();
          console.log(`从预览图表${i + 1}获取配置成功`);
        }

        // 如果没有找到配置，使用备用方法
        if (!chartOption) {
          console.log(`使用备用方法为图表${i + 1}生成配置`);

          // 根据图表容器ID判断类型
          if (chartContainer.id.includes("major-chart")) {
            const isARegion = chartContainer.id.includes("major-chart-0");
            chartOption = generateChartOption(isARegion);
          } else {
            // 默认图表配置
            chartOption = {
              title: {
                text: "图表数据",
                left: "center",
                textStyle: {
                  fontSize: 16,
                  color: "#333",
                },
              },
              tooltip: {},
              xAxis: {
                type: "category",
                data: ["数据1", "数据2", "数据3"],
              },
              yAxis: {
                type: "value",
              },
              series: [
                {
                  name: "数据",
                  type: "line",
                  data: [120, 200, 150],
                  lineStyle: { color: "#1bb394" },
                },
              ],
            };
          }
        }

        // 创建图表实例
        if (chartOption) {
          const chart = echarts.init(chartContainer);
          chart.setOption(chartOption);
          chartContainer._echarts_instance_ = chart;

          console.log(`图表${i + 1}创建成功`);

          // 等待图表渲染
          await new Promise((resolve) => setTimeout(resolve, 300));
          chart.resize();
        }
      } catch (chartError) {
        console.error(`创建图表${i + 1}失败:`, chartError);
      }
    }

    console.log("容器中所有图表重新创建完成");
  } catch (error) {
    console.error("在容器中重新创建图表失败:", error);
  }
};

const echartsARef = ref(null);
const echartsBRef = ref(null);
let chartA = null;
let chartB = null;

// 添加国家线数据状态
const nationalLineData = ref(null);
const showMajorAnalysis = ref(false);

// 处理网络图片转base64的函数
const convertNetworkImagesToBase64 = async (container) => {
  console.log("开始处理网络图片显示问题");

  // 处理 img 标签图片
  const images = container.querySelectorAll("img");
  const conversionPromises = [];

  for (let img of images) {
    if (
      img.src &&
      (img.src.startsWith("http://") || img.src.startsWith("https://"))
    ) {
      const conversionPromise = new Promise((resolve, reject) => {
        // 对于COS图片，由于已设置跨域，我们可以先尝试直接使用
        if (img.src.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")) {
          console.log("COS图片直接使用，无需转换:", img.src);

          // 验证图片是否能正常加载
          const testImg = new Image();
          testImg.crossOrigin = "anonymous";

          testImg.onload = () => {
            console.log("COS图片验证加载成功:", img.src);
            resolve();
          };

          testImg.onerror = () => {
            console.warn("COS图片验证失败，尝试转换为base64:", img.src);
            // 如果直接使用失败，则转换为base64
            convertToBase64(img, resolve);
          };

          testImg.src = img.src;
        } else {
          // 对于其他网络图片，转换为base64
          console.log("非COS图片，转换为base64:", img.src);
          convertToBase64(img, resolve);
        }
      });

      conversionPromises.push(conversionPromise);
    }
  }

  // 处理CSS背景图片
  const elementsWithBackgroundImages = container.querySelectorAll("*");
  for (let element of elementsWithBackgroundImages) {
    const computedStyle = window.getComputedStyle(element);
    const backgroundImage = computedStyle.backgroundImage;

    if (
      backgroundImage &&
      backgroundImage !== "none" &&
      backgroundImage.includes("url(")
    ) {
      // 提取背景图片URL
      const urlMatch = backgroundImage.match(/url\(["']?(.*?)["']?\)/);
      if (
        urlMatch &&
        urlMatch[1] &&
        (urlMatch[1].startsWith("http://") ||
          urlMatch[1].startsWith("https://"))
      ) {
        const imageUrl = urlMatch[1];
        console.log(
          "发现CSS背景图片:",
          imageUrl,
          "在元素:",
          element.className || element.tagName
        );

        const bgPromise = new Promise((resolve) => {
          convertBackgroundImageToBase64(element, imageUrl, resolve);
        });

        conversionPromises.push(bgPromise);
      }
    }
  }

  // 转换为base64的通用方法
  function convertToBase64(img, resolve) {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const tempImg = new Image();

    tempImg.crossOrigin = "anonymous";
    tempImg.referrerPolicy = "no-referrer";

    let retryCount = 0;
    const maxRetries = 3; // 增加重试次数

    const loadImage = () => {
      tempImg.onload = () => {
        if (tempImg.width === 0 || tempImg.height === 0) {
          console.warn("图片尺寸为0，保持原地址:", img.src);
          resolve();
          return;
        }

        canvas.width = tempImg.width;
        canvas.height = tempImg.height;

        try {
          ctx.drawImage(tempImg, 0, 0);
          const base64 = canvas.toDataURL("image/jpeg", 0.95); // 提高质量

          if (base64 && base64.length > 1000) {
            img.src = base64;
            console.log(
              "图片转换成功:",
              img.src.substring(0, 50) + "...",
              "原URL:",
              tempImg.src
            );
          } else {
            console.warn("base64数据过小，保持原地址:", img.src);
          }
          resolve();
        } catch (error) {
          console.warn("图片转换失败，保持原地址:", error);
          resolve();
        }
      };

      tempImg.onerror = () => {
        retryCount++;
        if (retryCount < maxRetries) {
          console.warn(
            `图片加载失败，正在重试 (${retryCount}/${maxRetries}):`,
            img.src
          );
          setTimeout(() => {
            loadImage();
          }, 2000 * retryCount); // 增加重试间隔
        } else {
          console.warn("图片加载失败，已达最大重试次数，保持原地址:", img.src);
          resolve();
        }
      };

      // 增加超时处理
      setTimeout(() => {
        if (tempImg.complete === false) {
          console.warn("图片加载超时，强制结束:", img.src);
          tempImg.src = ""; // 取消加载
          resolve();
        }
      }, 15000); // 增加超时时间到15秒

      // 开始加载图片
      const imageUrl = img.src;
      if (imageUrl.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")) {
        // 对于COS图片，可以不添加时间戳，因为已设置跨域
        tempImg.src = imageUrl;
      } else {
        const separator = imageUrl.includes("?") ? "&" : "?";
        tempImg.src = `${imageUrl}${separator}t=${Date.now()}`;
      }
    };

    loadImage();
  }

  // 处理CSS背景图片转base64
  function convertBackgroundImageToBase64(element, imageUrl, resolve) {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const tempImg = new Image();

    tempImg.crossOrigin = "anonymous";
    tempImg.referrerPolicy = "no-referrer";

    let retryCount = 0;
    const maxRetries = 3;

    const loadBgImage = () => {
      tempImg.onload = () => {
        if (tempImg.width === 0 || tempImg.height === 0) {
          console.warn("背景图片尺寸为0，保持原样式:", imageUrl);
          resolve();
          return;
        }

        canvas.width = tempImg.width;
        canvas.height = tempImg.height;

        try {
          ctx.drawImage(tempImg, 0, 0);
          const base64 = canvas.toDataURL("image/png", 0.95); // 使用PNG格式保持透明度

          if (base64 && base64.length > 1000) {
            // 更新元素的背景图片为base64
            element.style.backgroundImage = `url("${base64}")`;
            console.log("背景图片转换成功:", imageUrl, "-> base64");
          } else {
            console.warn("背景图片base64数据过小，保持原样式:", imageUrl);
          }
          resolve();
        } catch (error) {
          console.warn("背景图片转换失败，保持原样式:", error);
          resolve();
        }
      };

      tempImg.onerror = () => {
        retryCount++;
        if (retryCount < maxRetries) {
          console.warn(
            `背景图片加载失败，正在重试 (${retryCount}/${maxRetries}):`,
            imageUrl
          );
          setTimeout(() => {
            loadBgImage();
          }, 2000 * retryCount);
        } else {
          console.warn(
            "背景图片加载失败，已达最大重试次数，保持原样式:",
            imageUrl
          );
          resolve();
        }
      };

      // 增加超时处理
      setTimeout(() => {
        if (tempImg.complete === false) {
          console.warn("背景图片加载超时，强制结束:", imageUrl);
          tempImg.src = "";
          resolve();
        }
      }, 15000); // 15秒超时

      // 开始加载背景图片
      if (imageUrl.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")) {
        tempImg.src = imageUrl;
      } else {
        const separator = imageUrl.includes("?") ? "&" : "?";
        tempImg.src = `${imageUrl}${separator}t=${Date.now()}`;
      }
    };

    loadBgImage();
  }

  // 等待所有图片处理完成
  console.log("开始处理", conversionPromises.length, "个图片/背景图片");
  await Promise.all(conversionPromises);
  console.log("所有网络图片和背景图片处理完成");
};

// 强制设置所有内联样式
const forceInlineStyles = (container) => {
  console.log("开始强制设置内联样式...");

  // 处理所有step-num-tag元素，强制设置背景图片样式
  const stepNumTags = container.querySelectorAll(
    '[class*="step-num-tag"], .step-num-tag'
  );
  console.log(`找到 ${stepNumTags.length} 个step-num-tag元素需要强制设置样式`);

  stepNumTags.forEach((element, index) => {
    // 强制设置关键样式
    element.style.width = "265px";
    element.style.height = "40px";
    element.style.backgroundSize = "100% 100%";
    element.style.backgroundRepeat = "no-repeat";
    element.style.backgroundPosition = "center";
    element.style.display = "flex !important";
    element.style.alignItems = "center";
    element.style.marginBottom = "20px";
    element.style.visibility = "visible";
    element.style.opacity = "1";

    // 确保背景图片存在
    if (
      !element.style.backgroundImage ||
      !element.style.backgroundImage.includes("data:image")
    ) {
      // 如果没有背景图片，查找其他已转换的背景图片
      const otherStepTag = container.querySelector(
        '[class*="step-num-tag"][style*="data:image"]'
      );
      if (otherStepTag && otherStepTag.style.backgroundImage) {
        element.style.backgroundImage = otherStepTag.style.backgroundImage;
        console.log(`为step-num-tag ${index + 1} 设置备用背景图片`);
      }
    }

    console.log(
      `step-num-tag ${index + 1} 强制样式设置完成，背景图片:`,
      element.style.backgroundImage ? "YES" : "NO"
    );
  });

  // 处理所有图片元素
  const images = container.querySelectorAll("img");
  console.log(`找到 ${images.length} 个img元素需要强制设置样式`);

  images.forEach((img, index) => {
    img.style.display = "block";
    img.style.visibility = "visible";
    img.style.opacity = "1";
    img.style.maxWidth = "100%";
    img.style.height = "auto";

    // 确保COS图片有正确的跨域设置
    if (
      img.src &&
      img.src.includes("yqzx-1300870289.cos.ap-nanjing.myqcloud.com")
    ) {
      img.crossOrigin = "anonymous";
      img.referrerPolicy = "no-referrer";
    }

    console.log(
      `img ${index + 1} 强制样式设置完成，src:`,
      img.src ? "YES" : "NO"
    );
  });

  // 处理recommend-icon图片
  const recommendIconImages = container.querySelectorAll(
    '[class*="recommend-icon"] img, .recommend-icon img'
  );
  console.log(
    `找到 ${recommendIconImages.length} 个recommend-icon图片需要强制设置样式`
  );

  recommendIconImages.forEach((img, index) => {
    img.style.width = "27px";
    img.style.height = "21px";
    img.style.display = "inline-block";
    img.style.visibility = "visible";
    img.style.opacity = "1";
    console.log(`recommend-icon图片 ${index + 1} 强制样式设置完成`);
  });

  // 通用处理：确保所有元素可见
  const allElements = container.querySelectorAll("*");
  allElements.forEach((el) => {
    if (el.style && el.style.display === "none") {
      el.style.display = "block";
    }
    if (el.style && el.style.visibility === "hidden") {
      el.style.visibility = "visible";
    }
    if (el.style && el.style.opacity === "0") {
      el.style.opacity = "1";
    }
  });

  console.log("内联样式强制设置完成");
};

const handleMajorFocus = () => {
  // 检查是否已选择本科院校
  if (!reportForm.undergraduateSchool) {
    ElMessage.warning("请先选择本科院校");
    return;
  }

  // 如果还未初始化或者选项为空，则加载第一页数据
  if (!majorIsInitialized.value || majorOptions.value.length === 0) {
    majorCurrentPage.value = 1;
    remoteSearchMajor(""); // 传入空字符串，加载该院校所有专业
  }
};

// 加载更多专业
const loadMoreMajors = () => {
  if (majorHasMore.value && !majorSearchLoading.value) {
    majorCurrentPage.value += 1;
    remoteSearchMajor(lastMajorQuery.value);
  }
};

// 处理下拉框可见性变化
const handleMajorSelectVisibleChange = (visible) => {
  if (visible) {
    // 当下拉框打开时，可以在这里添加额外的逻辑
    console.log("本科专业下拉框打开");
  }
};

// 本科专业分页相关状态
const majorCurrentPage = ref(1);
const majorHasMore = ref(false);
const majorIsInitialized = ref(false);

// 用于存储上一次搜索的查询词
const lastMajorQuery = ref("");
</script>

<style scoped lang="less">
.container {
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
  position: relative;
  padding-top: 72px;
}
.generate-container {
  border-radius: 10px;
  padding: 0;
  background-color: #fff;
}

.report-container {
  background-color: #fff;
  margin-top: 20px;
  border-radius: 4px;
  height: calc(100vh - 67px - 92px);
  overflow: scroll;
  padding: 0 150px;
}

.credits-info {
  width: 100%;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 150px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

.credits-text {
  font-weight: bold;
  color: #ff9900;
}

.credits-count {
  font-weight: bold;
  color: #ff9900;
  font-size: 18px;
  margin: 0 5px;
}

.credits-unit {
  color: #666;
}

.button-group {
  width: 600px; /* 增加宽度以适应新按钮 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 15px; /* 使用gap替代margin-right */
}

.action-btn {
  width: 99px;
  height: 36px;
  background: #1bb394;
  box-shadow: 2px 7px 8px 1px rgba(0, 0, 0, 0.16);
  border-radius: 10px 10px 10px 10px;
  color: #fff;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  flex-shrink: 0; /* 防止按钮收缩 */
}

/* 重新生成按钮样式 */
.regenerate-btn {
  width: 140px; /* 更宽以适应文字 */
  &:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }
}

/* 步骤样式 */

.step-section {
  margin-bottom: 30px;
}

.step-header {
  margin-bottom: 15px;
}

.step-title {
  width: 320px;
  height: 40px;
  padding: 0 15px;
  padding-top: 14px;
  color: #fff;
  border-radius: 5px;
  font-weight: bold;
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/step-bg.png");
  background-repeat: no-repeat;
  text-align: center;
}

.step-content {
  padding: 20px 0;
  padding-top: 0;
  border-radius: 5px;
  position: relative;
  padding-bottom: 0;
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.step-num-tag {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-left: 20px;
}

.form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.wide-item {
  grid-column: span 2;
}

.item-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

.full-width {
  width: 100%;
}

.form-grid,
.english-grid,
.school-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper) {
    border-color: #1bb394 !important;

    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select__wrapper.is-focus) {
    border-color: #1bb394 !important;
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #1bb394 inset !important;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  /* 下拉菜单样式 */
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }

  /* 多选框样式 */
  :deep(.el-select .el-tag) {
    background-color: #e6f7f1;
    border-color: #1bb394;
    color: #1bb394;
  }

  /* select下拉箭头颜色 */
  :deep(.el-select .el-input__suffix) {
    color: #1bb394;
  }
}

.score-grid {
  /* 输入框样式 */
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #fff inset;
  }
}

/* 成绩表格 */
.score-grid,
.english-grid {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.score-row,
.english-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.score-item,
.english-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.score-item {
  border: #1bb394 1px solid;
  border-radius: 10px;
  padding-left: 10px;
  position: relative;
  cursor: move;
  /* 显示可拖动光标 */
  transition: all 0.2s ease;

  &.drag-over {
    border: 2px dashed #1bb394;
    background-color: rgba(27, 179, 148, 0.05);
    transform: scale(1.01);
  }

  .score-label {
    height: 100%;
    width: 180px;
    border-right: #666 1px solid;
    box-shadow: 2px 0 2px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-close {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.score-label,
.english-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 0;
  margin-right: 15px;
  white-space: nowrap;
  min-width: 120px;
}

.english-label {
  text-align: center;
}

.score-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  width: 100%;
}

.score-divider {
  margin: 0 5px;
  color: #999;
}

/* 学校选择 */
.school-grid {
  margin-left: 20px;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 表格样式 */
.score-table {
  width: 98.5%;
  border: 1px solid #1bb394;
  margin-top: 20px;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 20px;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
  font-weight: bold;
  border-bottom: 1px solid #1bb394;
}

.cell-color {
  background-color: #f5fffd;
}

.th-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 12px;
  color: #504e4e;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 40px;

  &:last-child {
    border-right: none;
  }

  .sel-no-border {
    :deep(.el-select__wrapper) {
      border-color: #fff !important;
      box-shadow: none;
      background-color: transparent;

      .el-select__placeholder {
        font-weight: 400;
        font-size: 12px;
        color: #504e4e;
      }
    }

    :deep(.el-select__selected-item) {
      text-align: center;
    }
  }

  .center-select {
    width: 55%;

    :deep(.el-input__inner) {
      text-align: center !important;
    }

    :deep(.el-input__suffix) {
      right: 5px;
    }
  }

  :deep(.center-select-dropdown) {
    .el-select-dropdown__item {
      text-align: center !important;
    }
  }
}

.table-row-score {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  text-align: center;
}

.td-cell {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #1bb394;
  text-align: center;
  height: 40px;

  &:last-child {
    border-right: none;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-input__inner) {
    text-align: center;
    box-shadow: none;
    font-weight: bold;
  }
}

.td-cell .el-input {
  width: 100%;
}

.table-input {
  width: 100%;
}

/* 个性化需求和薄弱模块 */
.personal-demands,
.expertise-advice {
  margin-left: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demands-label,
.advice-label {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  width: 100px;
  height: 30px;
  line-height: 30px;
}

.demands-input,
.advice-input {
  flex: 1;
}

/* 表单样式覆盖 */
:deep(.el-textarea__inner) {
  border-color: #1bb394 !important;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px #1bb394 inset !important;
}

/* 按钮样式 */
.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.action-button {
  width: 104px;
  height: 38px;
  background: #1bb394;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1bb394;
}

/* 英语栏目样式 */
.english-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.english-label {
  min-width: 100px;
  margin-right: 15px;
}

.no-border-select .el-input {
  border: none;
  box-shadow: none;
}

.no-border-select {
  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select .el-input__wrapper) {
    border-color: #1bb394 !important;
    border-radius: 8px;
    box-shadow: 0 0 0 1px #1bb394 inset;
  }

  :deep(.el-select-dropdown__item.selected) {
    color: #1bb394;
    font-weight: bold;
  }

  :deep(.el-select-dropdown__item:hover) {
    background-color: #e6f7f1;
  }
}

/* AI图标旋转动画样式 */
.ai-animation-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 40px;
}

.ai-icon-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  cursor: pointer;
  perspective: 1000px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.ai-icon-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.5s ease, background-image 0.3s ease;
  border-radius: 50%;
}

.ai-icon-layer.outer {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/ai-bg-1.png");
  z-index: 1;
  width: 200px;
  height: 200px;
  top: 0;
  left: 0;

  &.hover-effect {
    background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/ai-bg-1_hover.png");
  }
}

.ai-icon-layer.middle {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/ai-bg-2.png");
  z-index: 2;
  width: 160px;
  height: 160px;
  top: 20px;
  left: 20px;
}

.ai-icon-layer.inner {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/ai-bg-3.png");
  z-index: 3;
  width: 120px;
  height: 120px;
  top: 40px;
  left: 40px;
}

/* 遮罩层样式 */
.ai-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.ai-overlay-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-large-icon-wrapper {
  position: relative;
  width: 400px;
  height: 400px;
  perspective: 1000px;
}

.ai-large-icon-wrapper .ai-icon-layer.outer {
  width: 400px;
  height: 400px;
}

.ai-large-icon-wrapper .ai-icon-layer.middle {
  width: 320px;
  height: 320px;
  top: 40px;
  left: 40px;
}

.ai-large-icon-wrapper .ai-icon-layer.inner {
  width: 240px;
  height: 240px;
  top: 80px;
  left: 80px;
}

/* 动画效果 */
.animate-outer {
  animation: rotateOuter 5s ease-in-out infinite;
}

.animate-middle {
  animation: rotateMiddle 5s ease-in-out infinite;
}

.animate-inner {
  animation: pulseInner 5s ease-in-out infinite;
}

@keyframes rotateOuter {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(720deg);
  }
}

@keyframes rotateMiddle {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(-360deg);
  }

  100% {
    transform: rotate(-720deg);
  }
}

@keyframes pulseInner {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

/* 粒子背景效果 */
.ai-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at 50% 50%,
    rgba(27, 179, 148, 0.2) 0%,
    rgba(0, 0, 0, 0) 60%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }

  100% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

/* 添加光芒效果 */
.ai-large-icon-wrapper::after {
  content: "";
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background: radial-gradient(
    circle at center,
    rgba(27, 179, 148, 0.3) 0%,
    transparent 70%
  );
  z-index: -1;
  animation: glow 4s ease-in-out infinite;
}

@keyframes glow {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 0.3;
  }
}

.echarts-box {
  width: 98%;
  height: 180px;
  margin: 0 auto 18px auto;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
}

/* 院校总览表格样式 */
.school-table-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.school-table-header {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  height: 46px;
  line-height: 46px;
  text-align: center;
  padding: 0 10px;
}

.header-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.school-table-body {
  background-color: #fff;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  border-bottom: 1px solid #f0f0f0;
  height: 60px;
  line-height: 60px;
  text-align: center;
  padding: 0 10px;

  &:hover {
    background-color: #f5f7fa;
  }

  &:nth-child(even) {
    background-color: #f9f9f9;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.body-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
}

.school-name {
  text-align: left;
  font-weight: bold;
  position: relative;
}

.school-tags {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.tag {
  display: inline-block;
  padding: 1px 6px;
  line-height: 1.5;
  font-size: 12px;
  border-radius: 3px;
  color: white;
  font-weight: normal;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

.tag-double {
  background-color: #1bb394;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  margin: 20px 0;
  overflow: hidden;
}

.school-header {
  display: flex;
  padding: 20px;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 14px;
    color: #666;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    margin-right: 0;
  }

  .major-diff {
    font-size: 14px;
    color: #333;
    margin-left: 5px;
  }
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #2fc293;
}

.section-title {
  font-size: 18px;
  margin: 15px 0;
  font-weight: 600;
  position: relative;
  padding-left: 15px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   left: 0;
  //   top: 50%;
  //   transform: translateY(-50%);
  //   width: 4px;
  //   height: 18px;
  //   background-color: #1bb394;
  //   border-radius: 2px;
  // }
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 标签样式增强 */
.tag {
  padding: 2px 8px;
  font-size: 12px;
}

.tag-double {
  background-color: #1bb394;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
<style>
.el-message-box__btns .el-button--primary {
  background-color: #1bb394 !important;
  /* 绿色背景 */
  border: none !important;
}

.el-select-dropdown__item.is-selected {
  color: #1bb394 !important;
  font-weight: bold;
}
</style>

<style scoped>
/* 招生情况样式 */
.admission-section {
  margin-top: 30px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

.reexam-container {
  border: 1px solid #e8f5f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e8f5f0;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-icon {
  width: 6px;
  height: 6px;
  background-color: #1bb394;
  border-radius: 50%;
  margin-right: 10px;
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px solid #e8f5f0;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}

/* 预览弹窗样式 */
.preview-dialog {
  :deep(.el-dialog) {
    max-width: 95vw;
    margin: 0 auto;
  }

  :deep(.el-dialog__body) {
    padding: 10px 20px;
    max-height: 80vh;
    overflow-y: auto;
  }
}

.preview-container {
  width: 100%;
  min-height: 400px;
  position: relative;
}

.preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 16px;
  color: #666;
}

.preview-content {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 确保预览内容中的图表正确显示 */
  :deep([_echarts_instance_]) {
    width: 100% !important;
    height: 400px !important;
    display: block !important;
    visibility: visible !important;
  }

  /* 预览中的图表容器样式 */
  :deep(.echarts-box) {
    width: 100% !important;
    height: 400px !important;
    display: block !important;
    visibility: visible !important;
    border: 1px solid #1bb394;
    border-radius: 12px;
    background: #fff;
    margin: 10px 0;
    box-sizing: border-box;
  }

  :deep(.preview-chart) {
    width: 100% !important;
    height: 400px !important;
    display: block !important;
    visibility: visible !important;
    border: 1px solid #1bb394;
    border-radius: 12px;
    background: #fff;
    margin: 10px 0;
    box-sizing: border-box;
  }

  /* 专业分析图表样式 */
  :deep(.major-chart) {
    border: 1px solid #1bb394;
    border-radius: 12px;
    background: #fff;
    margin: 10px 0;
  }

  /* 报告内容图表样式 */
  :deep(.content-chart) {
    border: 1px solid #1bb394;
    border-radius: 12px;
    background: #fff;
    margin: 10px 0;
  }

  /* 确保专业分析容器在预览中正确显示 */
  :deep(.major-analysis-container) {
    width: 100%;
    padding: 0 20px;

    .step-section {
      /* margin-bottom: 30px;*/
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
    }

    .step-header {
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #1bb394;
      color: #fff;
      font-weight: bold;
    }

    .step-content {
      width: 100%;
      padding: 20px;
      padding-bottom: 0;
    }

    /* 预览中专业分析图表的样式 */
    .echarts-box {
      width: 98% !important;
      height: 280px !important;
      margin: 0 auto 18px auto !important;
      border: 1px solid #1bb394;
      border-radius: 12px;
      background: #fff;
      box-sizing: border-box;
      display: block !important;
      visibility: visible !important;
    }
  }

  /* 预览内容的基本样式 */
  :deep(.content-container) {
    padding: 0;
  }

  :deep(.step-title) {
    background-color: #1bb394;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
  }

  :deep(.step-content) {
    margin-bottom: 20px;
  }

  /* 学生基本信息样式 */
  :deep(.student-basic-info) {
    margin-bottom: 30px;

    h2 {
      color: #1bb394;
      text-align: center;
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: bold;
    }

    div {
      margin-bottom: 8px;
      line-height: 1.6;

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }

  /* 完整报告预览容器 */
  :deep(.full-report-preview) {
    width: 100%;

    .student-basic-info {
      border-bottom: 2px solid #1bb394;
      padding-bottom: 20px;
      margin-bottom: 30px;
    }

    .report-content {
      .content-container {
        padding: 0;
      }
    }
  }
}

.preview-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #999;
  font-size: 16px;
}
</style>
